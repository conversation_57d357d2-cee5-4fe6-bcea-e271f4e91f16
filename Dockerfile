# 使用官方 Node.js 18 Alpine 镜像作为基础镜像
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装 dumb-init 用于信号处理
RUN apk add --no-cache dumb-init

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# 只复制 package.json
COPY package.json ./

# 安装生产依赖
FROM base AS deps
RUN npm install --only=production && npm cache clean --force

# 开发阶段
FROM base AS dev
RUN npm install
COPY . .
USER nodejs
EXPOSE 8847
CMD ["dumb-init", "npm", "run", "dev"]

# 生产阶段
FROM base AS production
COPY --from=deps --chown=nodejs:nodejs /app/node_modules ./node_modules
COPY --chown=nodejs:nodejs . .

# 切换到非root用户
USER nodejs

# 暴露端口
EXPOSE 8847

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:8847/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动应用
CMD ["dumb-init", "npm", "start"] 