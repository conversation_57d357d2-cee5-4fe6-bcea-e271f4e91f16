#!/usr/bin/env node

/**
 * 测试审核接口
 * 验证名人图片审核功能
 */

const fetch = require('node-fetch');

// 服务器配置
const SERVER_URL = 'http://localhost:8847';

// 你提供的测试请求
const testRequest = {
    "messages": [
        {
            "role": "user",
            "content": [
                {
                    "text": "画面全程以第一人称自拍视角拍摄，镜头始终以自拍视角跟随角色移动。角色以主播的口吻向大家介绍当下所处的环境和正在发生的事。台词：\"嘿，家人们！看我身后，这就是霍格沃茨的大礼堂！是不是跟传说中一模一样？天花板上飘着蜡烛，还能看到外面的天空，太酷了！今天早餐有我最爱的南瓜汁，一会儿上课可不能迟到！\" 画面中，角色兴奋地对着镜头说话，镜头随着她的动作微微晃动，并短暂地扫过大礼堂的全景，展示漂浮的蜡烛和魔法天花板。背景音是学生们的嘈杂交谈声和餐具碰撞声。台词用中文来说。",
                    "type": "text"
                },
                {
                    "image_url": {
                        "url": "https://cdn.hailuoai.video/moss/prod/image_20250725_164352_49a6fd54.png"
                    },
                    "type": "image_url"
                }
            ]
        }
    ],
    "model": "veo3-pro-frames",
    "stream": true,
    "stream_options": {
        "include_usage": true
    }
};

/**
 * 检查服务器状态
 */
async function checkServerStatus() {
    try {
        console.log('🔍 检查服务器状态...');
        const response = await fetch(`${SERVER_URL}/api/moderation/stats`);
        
        if (response.ok) {
            const result = await response.json();
            console.log('✅ 服务器运行正常');
            console.log(`📊 审核配置:`);
            console.log(`   - 启用状态: ${result.data.config.enabled}`);
            console.log(`   - 严格程度: ${result.data.config.strictness}`);
            console.log(`   - 总请求数: ${result.data.totalRequests}`);
            console.log(`   - 通过数: ${result.data.approvedRequests}`);
            console.log(`   - 拒绝数: ${result.data.rejectedRequests}`);
            return true;
        } else {
            console.log('❌ 服务器响应异常:', response.status);
            return false;
        }
    } catch (error) {
        console.log('❌ 无法连接到服务器:', error.message);
        console.log('💡 请确保服务器已启动: npm start');
        return false;
    }
}

/**
 * 测试单独的文字内容审核
 */
async function testTextModeration() {
    console.log('\n📝 测试文字内容审核...');
    
    const textContent = testRequest.messages[0].content[0].text;
    console.log(`内容: ${textContent.substring(0, 100)}...`);
    
    try {
        const response = await fetch(`${SERVER_URL}/api/moderation/test`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                text: textContent
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log(`审核结果: ${result.data.approved ? '✅ 通过' : '❌ 不通过'}`);
            console.log(`置信度: ${result.data.confidence || 'N/A'}`);
            
            if (!result.data.approved) {
                console.log(`违规类型: ${result.data.violations?.join(', ') || '未知'}`);
                console.log(`拒绝原因: ${result.data.reason || '未知'}`);
                console.log(`改进建议: ${result.data.suggestions || '无'}`);
            }
            
            console.log(`Google Veo政策合规: ${result.data.veo_policy_compliance || '未知'}`);
        } else {
            console.log(`❌ 审核失败: ${result.error}`);
        }
    } catch (error) {
        console.log(`❌ 请求失败: ${error.message}`);
    }
}

/**
 * 测试图片内容审核
 */
async function testImageModeration() {
    console.log('\n🖼️  测试图片内容审核...');
    
    const imageUrl = testRequest.messages[0].content[1].image_url.url;
    console.log(`图片URL: ${imageUrl}`);
    
    try {
        const response = await fetch(`${SERVER_URL}/api/moderation/test`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                imageUrl: imageUrl
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log(`审核结果: ${result.data.approved ? '✅ 通过' : '❌ 不通过'}`);
            console.log(`置信度: ${result.data.confidence || 'N/A'}`);
            
            if (!result.data.approved) {
                console.log(`违规类型: ${result.data.violations?.join(', ') || '未知'}`);
                console.log(`拒绝原因: ${result.data.reason || '未知'}`);
                console.log(`改进建议: ${result.data.suggestions || '无'}`);
            }
            
            if (result.data.description) {
                console.log(`图片描述: ${result.data.description}`);
            }
            
            console.log(`Google Veo政策合规: ${result.data.veo_policy_compliance || '未知'}`);
        } else {
            console.log(`❌ 审核失败: ${result.error}`);
        }
    } catch (error) {
        console.log(`❌ 请求失败: ${error.message}`);
    }
}

/**
 * 测试完整请求审核（模拟实际视频生成请求）
 */
async function testCompleteRequest() {
    console.log('\n🎬 测试完整请求审核...');
    
    try {
        const response = await fetch(`${SERVER_URL}/api/video/submit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-api-key'
            },
            body: JSON.stringify(testRequest)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ 完整请求通过审核');
            console.log(`任务ID: ${result.data?.taskId || 'N/A'}`);
        } else {
            console.log('❌ 完整请求被拒绝');
            
            if (result.code === 'CONTENT_MODERATION_FAILED') {
                console.log('拒绝原因: 内容审核失败');
                console.log(`违规类型: ${result.details?.violations?.join(', ') || '未知'}`);
                console.log(`改进建议: ${result.details?.suggestions || '无'}`);
                console.log(`Google Veo政策合规: ${result.details?.veo_policy_compliance || '未知'}`);
            } else {
                console.log(`错误代码: ${result.code || 'UNKNOWN'}`);
                console.log(`错误信息: ${result.error || result.message || '未知错误'}`);
            }
        }
    } catch (error) {
        console.log(`❌ 请求失败: ${error.message}`);
    }
}

/**
 * 主测试函数
 */
async function main() {
    console.log('🚀 开始测试审核接口');
    console.log('=' * 50);
    
    // 1. 检查服务器状态
    const serverOk = await checkServerStatus();
    if (!serverOk) {
        process.exit(1);
    }
    
    // 2. 测试文字内容审核
    await testTextModeration();
    
    // 3. 测试图片内容审核
    await testImageModeration();
    
    // 4. 测试完整请求审核
    await testCompleteRequest();
    
    console.log('\n✨ 测试完成');
}

// 运行测试
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    checkServerStatus,
    testTextModeration,
    testImageModeration,
    testCompleteRequest
};
