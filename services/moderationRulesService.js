const fs = require('fs').promises;
const path = require('path');
const { SENSITIVE_WORDS } = require('../config/moderation');

/**
 * 审核规则管理服务
 */
class ModerationRulesService {
    constructor() {
        this.customRulesPath = path.join(__dirname, '../config/custom-rules.json');
        this.customSensitiveWordsPath = path.join(__dirname, '../config/custom-sensitive-words.json');
        this.customRules = {};
        this.customSensitiveWords = {};
        this.loadCustomRules();
    }

    /**
     * 加载自定义规则
     */
    async loadCustomRules() {
        try {
            // 加载自定义规则
            try {
                const rulesData = await fs.readFile(this.customRulesPath, 'utf8');
                this.customRules = JSON.parse(rulesData);
            } catch (error) {
                // 文件不存在时创建默认规则
                this.customRules = {
                    version: '1.0.0',
                    lastUpdated: new Date().toISOString(),
                    rules: {
                        textLength: {
                            enabled: true,
                            maxLength: 2000,
                            description: '文字内容最大长度限制'
                        },
                        imageCount: {
                            enabled: true,
                            maxCount: 3,
                            description: '单次请求最大图片数量'
                        },
                        bannedDomains: {
                            enabled: true,
                            domains: [],
                            description: '禁止的图片域名列表'
                        },
                        allowedFileTypes: {
                            enabled: true,
                            types: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
                            description: '允许的图片文件类型'
                        }
                    }
                };
                await this.saveCustomRules();
            }

            // 加载自定义敏感词
            try {
                const wordsData = await fs.readFile(this.customSensitiveWordsPath, 'utf8');
                this.customSensitiveWords = JSON.parse(wordsData);
            } catch (error) {
                // 文件不存在时创建默认敏感词库
                this.customSensitiveWords = {
                    version: '1.0.0',
                    lastUpdated: new Date().toISOString(),
                    categories: {
                        custom: {
                            description: '自定义敏感词',
                            words: []
                        },
                        whitelist: {
                            description: '白名单词汇（不会被拦截）',
                            words: []
                        }
                    }
                };
                await this.saveCustomSensitiveWords();
            }

        } catch (error) {
            console.error('Failed to load custom rules:', error);
        }
    }

    /**
     * 保存自定义规则
     */
    async saveCustomRules() {
        try {
            this.customRules.lastUpdated = new Date().toISOString();
            await fs.writeFile(this.customRulesPath, JSON.stringify(this.customRules, null, 2));
        } catch (error) {
            console.error('Failed to save custom rules:', error);
            throw error;
        }
    }

    /**
     * 保存自定义敏感词
     */
    async saveCustomSensitiveWords() {
        try {
            this.customSensitiveWords.lastUpdated = new Date().toISOString();
            await fs.writeFile(this.customSensitiveWordsPath, JSON.stringify(this.customSensitiveWords, null, 2));
        } catch (error) {
            console.error('Failed to save custom sensitive words:', error);
            throw error;
        }
    }

    /**
     * 获取所有规则
     */
    getAllRules() {
        return {
            default: {
                sensitiveWords: SENSITIVE_WORDS,
                description: '默认敏感词库'
            },
            custom: {
                rules: this.customRules,
                sensitiveWords: this.customSensitiveWords,
                description: '自定义规则和敏感词'
            }
        };
    }

    /**
     * 获取合并后的敏感词库
     */
    getMergedSensitiveWords() {
        const merged = { ...SENSITIVE_WORDS };
        
        // 添加自定义敏感词
        if (this.customSensitiveWords.categories.custom.words.length > 0) {
            merged.custom = this.customSensitiveWords.categories.custom.words;
        }
        
        return merged;
    }

    /**
     * 获取白名单词汇
     */
    getWhitelistWords() {
        return this.customSensitiveWords.categories.whitelist.words || [];
    }

    /**
     * 添加自定义敏感词
     */
    async addSensitiveWords(category, words) {
        if (!Array.isArray(words)) {
            throw new Error('Words must be an array');
        }

        if (!this.customSensitiveWords.categories[category]) {
            this.customSensitiveWords.categories[category] = {
                description: `自定义类别: ${category}`,
                words: []
            };
        }

        // 去重并添加
        const existingWords = new Set(this.customSensitiveWords.categories[category].words);
        words.forEach(word => existingWords.add(word.toLowerCase()));
        
        this.customSensitiveWords.categories[category].words = Array.from(existingWords);
        await this.saveCustomSensitiveWords();
        
        return {
            success: true,
            added: words.length,
            total: this.customSensitiveWords.categories[category].words.length
        };
    }

    /**
     * 移除敏感词
     */
    async removeSensitiveWords(category, words) {
        if (!Array.isArray(words)) {
            throw new Error('Words must be an array');
        }

        if (!this.customSensitiveWords.categories[category]) {
            throw new Error(`Category ${category} not found`);
        }

        const wordsToRemove = new Set(words.map(w => w.toLowerCase()));
        const originalCount = this.customSensitiveWords.categories[category].words.length;
        
        this.customSensitiveWords.categories[category].words = 
            this.customSensitiveWords.categories[category].words.filter(
                word => !wordsToRemove.has(word.toLowerCase())
            );
        
        await this.saveCustomSensitiveWords();
        
        const removedCount = originalCount - this.customSensitiveWords.categories[category].words.length;
        
        return {
            success: true,
            removed: removedCount,
            total: this.customSensitiveWords.categories[category].words.length
        };
    }

    /**
     * 更新规则
     */
    async updateRule(ruleName, ruleConfig) {
        if (!this.customRules.rules[ruleName]) {
            throw new Error(`Rule ${ruleName} not found`);
        }

        this.customRules.rules[ruleName] = {
            ...this.customRules.rules[ruleName],
            ...ruleConfig
        };

        await this.saveCustomRules();
        
        return {
            success: true,
            rule: this.customRules.rules[ruleName]
        };
    }

    /**
     * 添加新规则
     */
    async addRule(ruleName, ruleConfig) {
        if (this.customRules.rules[ruleName]) {
            throw new Error(`Rule ${ruleName} already exists`);
        }

        this.customRules.rules[ruleName] = {
            enabled: true,
            description: '',
            ...ruleConfig
        };

        await this.saveCustomRules();
        
        return {
            success: true,
            rule: this.customRules.rules[ruleName]
        };
    }

    /**
     * 删除规则
     */
    async deleteRule(ruleName) {
        if (!this.customRules.rules[ruleName]) {
            throw new Error(`Rule ${ruleName} not found`);
        }

        delete this.customRules.rules[ruleName];
        await this.saveCustomRules();
        
        return {
            success: true,
            message: `Rule ${ruleName} deleted`
        };
    }

    /**
     * 验证内容是否符合自定义规则
     */
    validateContent(content) {
        const violations = [];
        const rules = this.customRules.rules;

        // 检查文字长度
        if (rules.textLength?.enabled && content.text) {
            if (content.text.length > rules.textLength.maxLength) {
                violations.push({
                    rule: 'textLength',
                    message: `Text length (${content.text.length}) exceeds maximum (${rules.textLength.maxLength})`
                });
            }
        }

        // 检查图片数量
        if (rules.imageCount?.enabled && content.images) {
            if (content.images.length > rules.imageCount.maxCount) {
                violations.push({
                    rule: 'imageCount',
                    message: `Image count (${content.images.length}) exceeds maximum (${rules.imageCount.maxCount})`
                });
            }
        }

        // 检查禁止的域名
        if (rules.bannedDomains?.enabled && content.images) {
            for (const imageUrl of content.images) {
                try {
                    const url = new URL(imageUrl);
                    if (rules.bannedDomains.domains.includes(url.hostname)) {
                        violations.push({
                            rule: 'bannedDomains',
                            message: `Image from banned domain: ${url.hostname}`
                        });
                    }
                } catch (error) {
                    violations.push({
                        rule: 'invalidUrl',
                        message: `Invalid image URL: ${imageUrl}`
                    });
                }
            }
        }

        return {
            valid: violations.length === 0,
            violations
        };
    }

    /**
     * 导出规则配置
     */
    exportRules() {
        return {
            customRules: this.customRules,
            customSensitiveWords: this.customSensitiveWords,
            exportTime: new Date().toISOString()
        };
    }

    /**
     * 导入规则配置
     */
    async importRules(rulesData) {
        try {
            if (rulesData.customRules) {
                this.customRules = rulesData.customRules;
                await this.saveCustomRules();
            }

            if (rulesData.customSensitiveWords) {
                this.customSensitiveWords = rulesData.customSensitiveWords;
                await this.saveCustomSensitiveWords();
            }

            return {
                success: true,
                message: 'Rules imported successfully'
            };
        } catch (error) {
            throw new Error(`Failed to import rules: ${error.message}`);
        }
    }
}

module.exports = new ModerationRulesService();
