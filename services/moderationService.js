const fetch = require('node-fetch');
const crypto = require('crypto');
const {
    MODERATION_CONFIG,
    SENSITIVE_WORDS,
    MODERATION_PROMPTS,
    MODERATION_MESSAGES,
    STRICTNESS_LEVELS,
    detectLanguage
} = require('../config/moderation');

/**
 * 内容审核服务
 */
class ModerationService {
    constructor() {
        this.cache = new Map();
        this.stats = {
            totalRequests: 0,
            approvedRequests: 0,
            rejectedRequests: 0,
            cacheHits: 0
        };
    }

    /**
     * 审核完整请求内容
     */
    async moderateRequest(requestData) {
        if (!MODERATION_CONFIG.enabled) {
            return { approved: true, reason: 'Moderation disabled' };
        }

        this.stats.totalRequests++;

        // 检测内容语言
        let detectedLanguage = 'en'; // 默认英文
        let allTextContent = '';

        // 收集所有文字内容用于语言检测
        if (requestData.messages && requestData.messages.length > 0) {
            for (const message of requestData.messages) {
                if (message.content) {
                    if (typeof message.content === 'string') {
                        allTextContent += message.content + ' ';
                    } else if (Array.isArray(message.content)) {
                        for (const item of message.content) {
                            if (item.type === 'text' && item.text) {
                                allTextContent += item.text + ' ';
                            }
                        }
                    }
                }
            }
        }

        // 检测语言
        if (allTextContent.trim()) {
            detectedLanguage = detectLanguage(allTextContent);
        }

        try {
            const results = [];

            // 审核文字内容
            if (requestData.messages && requestData.messages.length > 0) {
                for (const message of requestData.messages) {
                    if (message.content) {
                        if (typeof message.content === 'string') {
                            // 简单文字内容
                            if (MODERATION_CONFIG.text.enabled) {
                                const textResult = await this.moderateText(message.content, detectedLanguage);
                                results.push({ type: 'text', content: message.content, ...textResult });
                            }
                        } else if (Array.isArray(message.content)) {
                            // 复合内容（文字+图片）
                            for (const item of message.content) {
                                if (item.type === 'text' && MODERATION_CONFIG.text.enabled) {
                                    const textResult = await this.moderateText(item.text, detectedLanguage);
                                    results.push({ type: 'text', content: item.text, ...textResult });
                                } else if (item.type === 'image_url') {
                                    const imageResult = await this.moderateImage(item.image_url.url, detectedLanguage);
                                    results.push({ type: 'image', content: item.image_url.url, ...imageResult });
                                }
                            }
                        }
                    }
                }
            }

            // 汇总审核结果
            const finalResult = this.aggregateResults(results, detectedLanguage);

            if (finalResult.approved) {
                this.stats.approvedRequests++;
            } else {
                this.stats.rejectedRequests++;
            }

            return finalResult;

        } catch (error) {
            console.error('Moderation error:', error);
            // 审核失败时的默认策略
            const messages = MODERATION_MESSAGES[detectedLanguage] || MODERATION_MESSAGES.en;
            return {
                approved: false,
                reason: messages.moderationServiceError,
                error: error.message
            };
        }
    }

    /**
     * 审核文字内容
     */
    async moderateText(text, language = 'en') {
        if (!text || typeof text !== 'string') {
            return { approved: true, reason: 'No text content' };
        }

        if (!MODERATION_CONFIG.text.enabled) {
            return { approved: true, reason: 'Text moderation disabled' };
        }

        // 生成缓存键（包含语言）
        const cacheKey = this.generateCacheKey('text', text + ':' + language);

        // 检查缓存
        if (MODERATION_CONFIG.cache.enabled && this.cache.has(cacheKey)) {
            this.stats.cacheHits++;
            return this.cache.get(cacheKey);
        }

        try {
            // 1. 快速敏感词检测
            const sensitiveWordResult = this.checkSensitiveWords(text, language);
            if (!sensitiveWordResult.approved) {
                const messages = MODERATION_MESSAGES[language] || MODERATION_MESSAGES.en;
                const result = {
                    approved: false,
                    confidence: 0.9,
                    violations: sensitiveWordResult.violations,
                    reason: 'Contains sensitive words',
                    suggestions: messages.removeInappropriateContent
                };
                this.cacheResult(cacheKey, result);
                return result;
            }

            // 2. LLM智能审核
            const llmResult = await this.moderateWithLLM(text, 'text', language);
            this.cacheResult(cacheKey, llmResult);
            return llmResult;

        } catch (error) {
            console.error('Text moderation error:', error);
            const messages = MODERATION_MESSAGES[language] || MODERATION_MESSAGES.en;
            return {
                approved: false,
                reason: messages.moderationServiceError,
                error: error.message
            };
        }
    }

    /**
     * 审核图片内容
     */
    async moderateImage(imageUrl, language = 'en') {
        if (!imageUrl || !MODERATION_CONFIG.image.enabled) {
            return { approved: true, reason: 'Image moderation disabled or no URL' };
        }

        // 生成缓存键（包含语言）
        const cacheKey = this.generateCacheKey('image', imageUrl + ':' + language);

        // 检查缓存
        if (MODERATION_CONFIG.cache.enabled && this.cache.has(cacheKey)) {
            this.stats.cacheHits++;
            return this.cache.get(cacheKey);
        }

        try {
            // 验证图片URL和格式
            const urlValidation = this.validateImageUrl(imageUrl, language);
            if (!urlValidation.valid) {
                return {
                    approved: false,
                    reason: urlValidation.reason,
                    suggestions: urlValidation.suggestions
                };
            }

            // LLM图片审核
            const llmResult = await this.moderateWithLLM(imageUrl, 'image', language);
            this.cacheResult(cacheKey, llmResult);
            return llmResult;

        } catch (error) {
            console.error('Image moderation error:', error);
            const messages = MODERATION_MESSAGES[language] || MODERATION_MESSAGES.en;
            return {
                approved: false,
                reason: messages.moderationServiceError,
                error: error.message
            };
        }
    }

    /**
     * 敏感词检测
     */
    checkSensitiveWords(text, language = 'en') {
        const violations = [];
        const lowerText = text.toLowerCase();

        // 违规类型的多语言映射
        const violationTranslations = {
            zh: {
                violence: '暴力和危险内容',
                sexual: '成人和性内容',
                hate: '仇恨言论和歧视',
                illegal: '非法活动',
                child_safety: '儿童安全问题',
                misinformation: '误导信息',
                copyright: '版权和知识产权',
                privacy: '隐私侵犯',
                political: '政治敏感内容'
            },
            en: {
                violence: 'Violence and Dangerous Content',
                sexual: 'Adult and Sexual Content',
                hate: 'Hate Speech and Discrimination',
                illegal: 'Illegal Activities',
                child_safety: 'Child Safety Issues',
                misinformation: 'Misinformation',
                copyright: 'Copyright and Intellectual Property',
                privacy: 'Privacy Violation',
                political: 'Political Sensitive Content'
            }
        };

        for (const [category, words] of Object.entries(SENSITIVE_WORDS)) {
            for (const word of words) {
                if (lowerText.includes(word.toLowerCase())) {
                    const translations = violationTranslations[language] || violationTranslations.en;
                    violations.push(translations[category] || category);
                    break;
                }
            }
        }

        return {
            approved: violations.length === 0,
            violations: [...new Set(violations)]
        };
    }

    /**
     * 使用LLM进行智能审核
     */
    async moderateWithLLM(content, type, language = 'en') {
        if (!MODERATION_CONFIG.llm.apiKey) {
            console.warn('LLM API key not configured, using basic rule-based moderation only');
            const messages = MODERATION_MESSAGES[language] || MODERATION_MESSAGES.en;
            // 当没有LLM时，返回基于规则的基本审核结果
            return {
                approved: true,
                reason: 'LLM moderation not configured - using rule-based moderation only',
                confidence: 0.7,
                violations: [],
                suggestions: messages.llmModerationNotConfigured,
                veo_policy_compliance: 'partial'
            };
        }

        // 选择对应语言的提示词
        const prompts = MODERATION_PROMPTS[language] || MODERATION_PROMPTS.en;
        const prompt = type === 'text'
            ? prompts.textModeration.replace('{content}', content)
            : prompts.imageModeration;

        const finalPrompt = prompt.replace('{strictness}', MODERATION_CONFIG.strictness);

        const messages = type === 'text' 
            ? [{ role: 'user', content: finalPrompt }]
            : [{ 
                role: 'user', 
                content: [
                    { type: 'text', text: finalPrompt },
                    { type: 'image_url', image_url: { url: content } }
                ]
            }];

        try {
            const response = await fetch(MODERATION_CONFIG.llm.url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${MODERATION_CONFIG.llm.apiKey}`
                },
                body: JSON.stringify({
                    model: MODERATION_CONFIG.llm.model,
                    messages: messages,
                    temperature: 0.1,
                    max_tokens: 500
                }),
                timeout: MODERATION_CONFIG.llm.timeout
            });

            if (!response.ok) {
                throw new Error(`LLM API error: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            const resultText = data.choices[0].message.content;
            
            // 解析JSON结果（兼容Markdown格式）
            try {
                const result = this.parseJsonFromResponse(resultText);

                // 根据严格程度调整结果
                const threshold = STRICTNESS_LEVELS[MODERATION_CONFIG.strictness].threshold;
                if (result.confidence < threshold) {
                    result.approved = false;
                    result.reason = `Content confidence (${result.confidence}) below threshold (${threshold}) for Google Veo policy compliance`;
                }

                // 确保包含Google Veo政策合规性信息
                if (!result.veo_policy_compliance) {
                    result.veo_policy_compliance = result.approved ? 'compliant' : 'non-compliant';
                }

                // 如果违反Google Veo政策，强制设为不通过
                if (result.veo_policy_compliance === 'non-compliant') {
                    result.approved = false;
                    if (!result.reason.includes('Google Veo')) {
                        result.reason = `Content violates Google Veo content policy: ${result.reason}`;
                    }
                }

                return result;
            } catch (parseError) {
                console.error('Failed to parse LLM response:', parseError);
                console.error('Original response:', resultText);

                // 尝试基于响应文本进行简单的规则判断
                const fallbackResult = this.fallbackModerationAnalysis(resultText, language);
                if (fallbackResult) {
                    console.warn('Using fallback analysis due to JSON parse failure');
                    return fallbackResult;
                }

                const messages = MODERATION_MESSAGES[language] || MODERATION_MESSAGES.en;
                return {
                    approved: false,
                    reason: messages.moderationServiceError,
                    error: parseError.message,
                    originalResponse: resultText.substring(0, 200) + (resultText.length > 200 ? '...' : '')
                };
            }

        } catch (error) {
            console.error('LLM moderation error:', error);
            return {
                approved: false,
                reason: 'LLM moderation failed',
                error: error.message
            };
        }
    }

    /**
     * 验证图片URL
     */
    validateImageUrl(url, language = 'en') {
        const messages = MODERATION_MESSAGES[language] || MODERATION_MESSAGES.en;

        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname.toLowerCase();
            const extension = pathname.split('.').pop();

            if (!MODERATION_CONFIG.image.supportedFormats.includes(extension)) {
                return {
                    valid: false,
                    reason: language === 'zh'
                        ? `不支持的图片格式: ${extension}`
                        : `Unsupported image format: ${extension}`,
                    suggestions: language === 'zh'
                        ? '请提供有效的图片URL（支持JPG、PNG、GIF、WebP格式）'
                        : 'Please provide a valid image URL (supports JPG, PNG, GIF, WebP formats)'
                };
            }

            return { valid: true };
        } catch (error) {
            return {
                valid: false,
                reason: language === 'zh' ? '无效的URL格式' : 'Invalid URL format',
                suggestions: language === 'zh'
                    ? '请提供有效的图片URL'
                    : 'Please provide a valid image URL'
            };
        }
    }

    /**
     * 汇总多个审核结果
     */
    aggregateResults(results, language = 'en') {
        const messages = MODERATION_MESSAGES[language] || MODERATION_MESSAGES.en;

        if (results.length === 0) {
            return {
                approved: true,
                reason: language === 'zh' ? '没有需要审核的内容' : 'No content to moderate'
            };
        }

        const rejectedResults = results.filter(r => !r.approved);

        if (rejectedResults.length === 0) {
            return {
                approved: true,
                reason: language === 'zh' ? '所有内容审核通过' : 'All content approved',
                details: results
            };
        }

        // 汇总违规信息
        const allViolations = [];
        const allSuggestions = [];

        rejectedResults.forEach(result => {
            if (result.violations) {
                allViolations.push(...result.violations);
            }
            if (result.suggestions) {
                allSuggestions.push(result.suggestions);
            }
        });

        return {
            approved: false,
            reason: messages.contentModerationFailed,
            violations: [...new Set(allViolations)],
            suggestions: [...new Set(allSuggestions)].join('; '),
            rejectedCount: rejectedResults.length,
            totalCount: results.length,
            details: results
        };
    }

    /**
     * 当JSON解析失败时的降级分析
     */
    fallbackModerationAnalysis(responseText, language = 'en') {
        if (!responseText || typeof responseText !== 'string') {
            return null;
        }

        const lowerResponse = responseText.toLowerCase();
        const messages = MODERATION_MESSAGES[language] || MODERATION_MESSAGES.en;

        // 检查明显的拒绝关键词
        const rejectKeywords = [
            'approved": false', 'approved":false', 'approved: false',
            'reject', 'violation', 'inappropriate', 'not allowed',
            'policy violation', 'content violates', 'not compliant',
            '不通过', '违规', '不合规', '拒绝', '不当', '违反'
        ];

        const hasRejectKeywords = rejectKeywords.some(keyword =>
            lowerResponse.includes(keyword.toLowerCase())
        );

        // 检查明显的通过关键词
        const approveKeywords = [
            'approved": true', 'approved":true', 'approved: true',
            'approve', 'compliant', 'acceptable', 'appropriate',
            'no violation', 'content is safe', 'looks good',
            '通过', '合规', '可以', '安全', '没有问题'
        ];

        const hasApproveKeywords = approveKeywords.some(keyword =>
            lowerResponse.includes(keyword.toLowerCase())
        );

        // 基于关键词判断
        if (hasRejectKeywords && !hasApproveKeywords) {
            return {
                approved: false,
                confidence: 0.6,
                violations: ['unknown'],
                reason: 'Content appears to violate policies based on response analysis',
                suggestions: messages.removeInappropriateContent,
                veo_policy_compliance: 'non-compliant',
                fallbackAnalysis: true
            };
        } else if (hasApproveKeywords && !hasRejectKeywords) {
            return {
                approved: true,
                confidence: 0.7,
                violations: [],
                reason: 'Content appears to be compliant based on response analysis',
                suggestions: '',
                veo_policy_compliance: 'compliant',
                fallbackAnalysis: true
            };
        }

        // 如果无法确定，默认拒绝（安全策略）
        return {
            approved: false,
            confidence: 0.3,
            violations: ['unknown'],
            reason: 'Unable to parse response, defaulting to rejection for safety',
            suggestions: messages.removeInappropriateContent,
            veo_policy_compliance: 'unknown',
            fallbackAnalysis: true
        };
    }

    /**
     * 解析LLM响应中的JSON（兼容Markdown格式）
     */
    parseJsonFromResponse(responseText) {
        if (!responseText) {
            throw new Error('Empty response text');
        }

        // 尝试直接解析JSON
        try {
            return JSON.parse(responseText);
        } catch (directParseError) {
            // 如果直接解析失败，尝试提取Markdown代码块中的JSON
            console.log('Direct JSON parse failed, trying to extract from markdown...');
        }

        // 提取Markdown代码块中的JSON
        const jsonBlockRegex = /```(?:json)?\s*\n?([\s\S]*?)\n?```/i;
        const match = responseText.match(jsonBlockRegex);

        if (match && match[1]) {
            try {
                const jsonContent = match[1].trim();
                return JSON.parse(jsonContent);
            } catch (blockParseError) {
                console.error('Failed to parse JSON from markdown block:', blockParseError);
                console.error('Extracted content:', match[1]);
            }
        }

        // 尝试查找JSON对象的开始和结束
        const jsonStartRegex = /\{[\s\S]*\}/;
        const jsonMatch = responseText.match(jsonStartRegex);

        if (jsonMatch) {
            try {
                return JSON.parse(jsonMatch[0]);
            } catch (objectParseError) {
                console.error('Failed to parse extracted JSON object:', objectParseError);
                console.error('Extracted object:', jsonMatch[0]);
            }
        }

        // 如果所有方法都失败，尝试清理常见的格式问题
        let cleanedText = responseText
            .replace(/```json\s*/gi, '')  // 移除开始的markdown标记
            .replace(/```\s*$/gi, '')     // 移除结束的markdown标记
            .replace(/^\s*[\r\n]+/gm, '') // 移除空行
            .trim();

        try {
            return JSON.parse(cleanedText);
        } catch (finalParseError) {
            console.error('All JSON parsing attempts failed');
            console.error('Original response:', responseText);
            console.error('Cleaned text:', cleanedText);
            throw new Error(`Unable to parse JSON from response: ${finalParseError.message}`);
        }
    }

    /**
     * 生成缓存键
     */
    generateCacheKey(type, content) {
        return crypto.createHash('md5').update(`${type}:${content}`).digest('hex');
    }

    /**
     * 缓存结果
     */
    cacheResult(key, result) {
        if (MODERATION_CONFIG.cache.enabled) {
            this.cache.set(key, result);
            
            // 设置过期时间
            setTimeout(() => {
                this.cache.delete(key);
            }, MODERATION_CONFIG.cache.ttl * 1000);
        }
    }

    /**
     * 获取统计信息
     */
    getStats() {
        return {
            ...this.stats,
            cacheSize: this.cache.size,
            approvalRate: this.stats.totalRequests > 0 
                ? (this.stats.approvedRequests / this.stats.totalRequests * 100).toFixed(2) + '%'
                : '0%'
        };
    }

    /**
     * 清理缓存
     */
    clearCache() {
        this.cache.clear();
    }
}

module.exports = new ModerationService();
