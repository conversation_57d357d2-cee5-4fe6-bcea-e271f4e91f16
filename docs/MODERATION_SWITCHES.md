# 审核开关配置指南 / Moderation Switches Configuration Guide

## 概述 / Overview

本系统支持灵活的审核开关配置，允许您根据需要选择性地启用或禁用文本审核和图片审核功能。

This system supports flexible moderation switch configuration, allowing you to selectively enable or disable text and image moderation features as needed.

## 配置选项 / Configuration Options

### 1. 总体审核开关 / Global Moderation Switch

```bash
# 控制整个审核系统的开关
MODERATION_ENABLED=true|false
```

- `true`: 启用审核系统 / Enable moderation system
- `false`: 完全关闭审核，所有内容直接通过 / Completely disable moderation, all content passes through

### 2. 文本审核开关 / Text Moderation Switch

```bash
# 控制文本内容审核
TEXT_MODERATION_ENABLED=true|false
```

- `true`: 审核文本内容 / Moderate text content
- `false`: 跳过文本审核 / Skip text moderation

### 3. 图片审核开关 / Image Moderation Switch

```bash
# 控制图片内容审核
IMAGE_MODERATION_ENABLED=true|false
```

- `true`: 审核图片内容 / Moderate image content
- `false`: 跳过图片审核 / Skip image moderation

## 常见配置场景 / Common Configuration Scenarios

### 场景 1: 只审核图片，不审核文本 / Scenario 1: Image Only Moderation

```bash
MODERATION_ENABLED=true
TEXT_MODERATION_ENABLED=false
IMAGE_MODERATION_ENABLED=true
```

**适用场景 / Use Cases:**
- 信任用户的文本输入，但需要确保图片内容安全
- 减少文本审核的延迟，专注于图片内容控制
- Trust user text input but need to ensure image content safety
- Reduce text moderation latency while focusing on image content control

### 场景 2: 只审核文本，不审核图片 / Scenario 2: Text Only Moderation

```bash
MODERATION_ENABLED=true
TEXT_MODERATION_ENABLED=true
IMAGE_MODERATION_ENABLED=false
```

**适用场景 / Use Cases:**
- 图片来源可信，但需要过滤文本中的敏感内容
- 节省图片审核的计算资源
- Trusted image sources but need to filter sensitive content in text
- Save computational resources for image moderation

### 场景 3: 同时审核文本和图片 / Scenario 3: Full Moderation

```bash
MODERATION_ENABLED=true
TEXT_MODERATION_ENABLED=true
IMAGE_MODERATION_ENABLED=true
```

**适用场景 / Use Cases:**
- 最高安全级别，全面内容审核
- 公开服务或高风险应用
- Highest security level with comprehensive content moderation
- Public services or high-risk applications

### 场景 4: 完全关闭审核 / Scenario 4: Disable All Moderation

```bash
MODERATION_ENABLED=false
```

**适用场景 / Use Cases:**
- 开发测试环境
- 内部可信用户环境
- Development and testing environments
- Internal trusted user environments

## 配置优先级 / Configuration Priority

1. **总体开关优先级最高** / **Global switch has highest priority**
   - 当 `MODERATION_ENABLED=false` 时，所有审核都被跳过
   - When `MODERATION_ENABLED=false`, all moderation is skipped

2. **分类开关在总体开关启用时生效** / **Category switches take effect when global switch is enabled**
   - 只有在 `MODERATION_ENABLED=true` 时，`TEXT_MODERATION_ENABLED` 和 `IMAGE_MODERATION_ENABLED` 才有效
   - `TEXT_MODERATION_ENABLED` and `IMAGE_MODERATION_ENABLED` only work when `MODERATION_ENABLED=true`

## 环境变量配置示例 / Environment Variables Example

创建 `.env` 文件或在系统环境变量中设置：

Create `.env` file or set in system environment variables:

```bash
# 基础配置 / Basic Configuration
MODERATION_ENABLED=true
TEXT_MODERATION_ENABLED=false
IMAGE_MODERATION_ENABLED=true

# 审核严格程度 / Moderation Strictness
MODERATION_STRICTNESS=normal

# LLM 配置 / LLM Configuration
MODERATION_LLM_URL=https://api.openai.com/v1/chat/completions
MODERATION_LLM_MODEL=gpt-4o-mini
MODERATION_LLM_API_KEY=your_api_key_here
```

## 测试配置 / Testing Configuration

使用提供的测试脚本验证配置：

Use the provided test script to verify configuration:

```bash
node test/moderation-switches.test.js
```

## 监控和日志 / Monitoring and Logging

系统会在日志中记录审核开关状态：

The system logs moderation switch status:

```javascript
// 获取审核统计信息
const stats = moderationService.getStats();
console.log('审核统计:', stats);
```

## 注意事项 / Important Notes

1. **配置变更需要重启服务** / **Configuration changes require service restart**
2. **建议在生产环境中启用缓存** / **Recommend enabling cache in production**
3. **定期检查审核效果和性能** / **Regularly check moderation effectiveness and performance**
4. **确保 LLM API 密钥的安全性** / **Ensure security of LLM API keys**

## 故障排除 / Troubleshooting

### 问题：配置不生效 / Issue: Configuration not taking effect

**解决方案 / Solution:**
1. 检查环境变量是否正确设置
2. 重启服务
3. 检查日志中的配置加载信息

### 问题：审核结果不符合预期 / Issue: Unexpected moderation results

**解决方案 / Solution:**
1. 运行测试脚本验证配置
2. 检查审核严格程度设置
3. 查看详细的审核日志
