# JSON解析兼容性说明

## 问题背景

大模型（如GPT-4、Claude等）在返回JSON响应时，经常会使用Markdown格式包装JSON内容，导致标准JSON解析失败。

常见的响应格式包括：

### 1. 标准JSON格式
```json
{
  "approved": true,
  "confidence": 0.9,
  "violations": [],
  "suggestions": "Content looks good"
}
```

### 2. Markdown代码块格式
```
```json
{
  "approved": false,
  "confidence": 0.3,
  "violations": ["violence"],
  "suggestions": "Remove violent content"
}
```
```

### 3. 带说明文字的格式
```
Based on my analysis, here is the result:

```json
{
  "approved": false,
  "confidence": 0.2,
  "violations": ["violence", "inappropriate"],
  "suggestions": "Please modify the content"
}
```

This content should be rejected.
```

## 解决方案

我们的系统现在支持多种JSON解析策略：

### 1. 直接JSON解析
首先尝试直接解析响应文本作为JSON。

### 2. Markdown代码块提取
如果直接解析失败，尝试提取```json```代码块中的内容。

### 3. JSON对象匹配
使用正则表达式查找响应中的JSON对象。

### 4. 文本清理
移除常见的Markdown标记和格式问题后重新解析。

### 5. 降级分析
如果所有JSON解析都失败，基于响应文本中的关键词进行简单分析：

- **拒绝关键词**: "violation", "inappropriate", "not allowed", "违规", "不当"等
- **通过关键词**: "approved", "compliant", "acceptable", "通过", "合规"等

## 使用示例

### 测试JSON解析功能
```bash
npm run test-json-parsing
```

### 在代码中使用
```javascript
const moderationService = require('./services/moderationService');

try {
    const result = moderationService.parseJsonFromResponse(llmResponse);
    console.log('解析成功:', result);
} catch (error) {
    console.error('解析失败:', error.message);
}
```

## 错误处理

当JSON解析完全失败时，系统会：

1. **记录详细错误信息**：包括原始响应和解析错误
2. **使用降级分析**：基于文本关键词判断审核结果
3. **安全默认策略**：无法确定时默认拒绝内容

## 降级分析示例

### 拒绝内容的文本响应
```
The content violates our policy and should be rejected.
```
**降级分析结果**:
```json
{
  "approved": false,
  "confidence": 0.6,
  "violations": ["unknown"],
  "reason": "Content appears to violate policies based on response analysis",
  "fallbackAnalysis": true
}
```

### 通过内容的文本响应
```
The content looks good and is approved.
```
**降级分析结果**:
```json
{
  "approved": true,
  "confidence": 0.7,
  "violations": [],
  "reason": "Content appears to be compliant based on response analysis",
  "fallbackAnalysis": true
}
```

## 配置选项

系统会自动处理各种格式，无需额外配置。但你可以通过以下方式监控解析状态：

### 检查是否使用了降级分析
```javascript
if (result.fallbackAnalysis) {
    console.log('使用了降级分析，建议检查LLM响应格式');
}
```

### 查看原始响应（解析失败时）
```javascript
if (result.originalResponse) {
    console.log('原始响应:', result.originalResponse);
}
```

## 最佳实践

1. **监控解析失败率**：定期检查是否有大量降级分析
2. **优化提示词**：确保LLM返回标准JSON格式
3. **日志记录**：保留解析失败的原始响应用于调试
4. **测试验证**：使用测试脚本验证各种响应格式

## 故障排除

### 常见问题

1. **JSON格式错误**
   - 检查是否有多余的逗号或注释
   - 确保字符串正确转义

2. **编码问题**
   - 确保响应使用UTF-8编码
   - 检查是否有特殊字符

3. **响应截断**
   - 检查LLM响应是否完整
   - 增加max_tokens限制

### 调试步骤

1. **查看原始响应**：
   ```bash
   # 启用调试模式
   DEBUG=* npm start
   ```

2. **运行解析测试**：
   ```bash
   npm run test-json-parsing
   ```

3. **检查日志**：
   ```bash
   # 查看解析错误日志
   grep "Failed to parse" logs/app.log
   ```

## 性能影响

- **正常情况**：JSON解析性能影响微乎其微
- **降级分析**：增加约1-2ms处理时间
- **缓存机制**：相同内容会使用缓存结果

## 更新日志

- **v1.0**: 基础JSON解析
- **v1.1**: 添加Markdown代码块支持
- **v1.2**: 添加降级分析和错误恢复
- **v1.3**: 优化多语言支持和错误处理
