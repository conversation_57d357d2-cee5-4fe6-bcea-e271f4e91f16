# Google Veo内容审核系统设置指南

## 快速开始

### 1. 环境配置

复制环境变量配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键参数：

```bash
# 启用内容审核
MODERATION_ENABLED=true

# 设置审核严格程度 (strict/normal/loose)
MODERATION_STRICTNESS=normal

# 配置大模型API（用于智能审核）
MODERATION_LLM_API_KEY=your-openai-api-key-here
MODERATION_LLM_MODEL=gpt-4o-mini
```

### 2. 启动服务

```bash
npm install
npm start
```

服务启动后，你会看到类似输出：
```
Video generation proxy server running on port 8847
=== Content Moderation Interface ===
Test content: POST http://localhost:8847/api/moderation/test
View stats: GET http://localhost:8847/api/moderation/stats
Manage rules: GET http://localhost:8847/api/moderation/rules

=== Moderation Status ===
Enabled: true
Strictness: normal
Image moderation: true
Cache enabled: true
```

### 3. 测试审核功能

使用提供的示例脚本测试：
```bash
node examples/moderation-examples.js
```

## 系统架构

```
客户请求 → 内容审核中间件 → 视频生成服务
           ↓
       审核不通过
           ↓
       返回改进建议
```

### 核心组件

1. **审核配置** (`config/moderation.js`)
   - Google Veo政策相关的审核提示词
   - 敏感词库（按违规类型分类）
   - 审核严格程度配置

2. **审核服务** (`services/moderationService.js`)
   - 文字内容审核
   - 图片内容审核
   - 结果缓存和统计

3. **审核中间件** (`middleware/moderationMiddleware.js`)
   - 自动拦截所有视频生成请求
   - 速率限制和白名单功能
   - 统计信息收集

4. **规则管理** (`services/moderationRulesService.js`)
   - 自定义敏感词管理
   - 审核规则配置
   - 规则导入导出

## Google Veo政策合规

系统专门针对Google Veo视频生成政策进行了优化，重点检测：

### 禁止内容类型

1. **暴力和危险内容**
   - 极端暴力、血腥场面
   - 自残、自杀相关内容
   - 危险活动指导

2. **成人和性内容**
   - 性暗示或性行为描述
   - 裸体或半裸体内容
   - 成人娱乐相关内容

3. **仇恨言论和歧视**
   - 基于种族、性别、宗教的歧视
   - 仇恨言论或煽动仇恨
   - 骚扰或霸凌内容

4. **儿童安全**
   - 涉及未成年人的不当内容
   - 儿童剥削相关内容

5. **非法活动**
   - 毒品制造或销售
   - 非法武器交易
   - 其他违法犯罪活动

6. **误导信息**
   - 医疗误导信息
   - 选举相关虚假信息
   - 阴谋论或极端观点

7. **知识产权侵权**
   - 未经授权使用受版权保护的内容
   - 商标侵权内容

8. **隐私侵犯**
   - 未经同意分享他人私人信息
   - 恶意跟踪或监视内容

## API使用示例

### 基本视频生成请求

```javascript
const response = await fetch('/api/video/submit', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token'
  },
  body: JSON.stringify({
    messages: [{
      role: "user",
      content: [{
        type: "text",
        text: "一个美丽的自然风景视频"
      }, {
        type: "image_url",
        image_url: { url: "https://example.com/image.jpg" }
      }]
    }],
    model: "veo3-pro-frames"
  })
});
```

### 审核失败响应

```json
{
  "success": false,
  "error": "Content moderation failed",
  "code": "CONTENT_MODERATION_FAILED",
  "details": {
    "reason": "Content violates Google Veo content policy",
    "violations": ["violence", "inappropriate"],
    "suggestions": "请移除暴力内容并使用更合适的表达方式，确保符合Google Veo视频生成政策",
    "rejectedCount": 1,
    "totalCount": 2
  }
}
```

### 预先测试内容

```javascript
const testResponse = await fetch('/api/moderation/test', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    text: "要测试的文字内容",
    imageUrl: "https://example.com/test-image.jpg"
  })
});
```

## 配置选项

### 审核严格程度

| 模式 | 阈值 | 适用场景 |
|------|------|----------|
| strict | 0.3 | 高风险应用，零容忍政策 |
| normal | 0.6 | 一般应用，平衡安全性和可用性 |
| loose | 0.8 | 创意应用，仅拦截明显违规内容 |

### 自定义敏感词管理

```javascript
// 添加自定义敏感词
await fetch('/api/moderation/sensitive-words', {
  method: 'POST',
  body: JSON.stringify({
    category: 'custom',
    words: ['自定义敏感词1', '自定义敏感词2']
  })
});

// 删除敏感词
await fetch('/api/moderation/sensitive-words', {
  method: 'DELETE',
  body: JSON.stringify({
    category: 'custom',
    words: ['要删除的词']
  })
});
```

## 监控和统计

### 查看审核统计

```bash
curl http://localhost:8847/api/moderation/stats
```

返回信息包括：
- 总请求数和通过率
- 缓存命中率
- 当前配置状态

### 清理缓存

```bash
curl -X POST http://localhost:8847/api/moderation/clear-cache
```

## 故障排除

### 常见问题

1. **审核服务无法启动**
   ```bash
   # 检查API密钥配置
   echo $MODERATION_LLM_API_KEY
   
   # 检查网络连接
   curl -I https://api.openai.com
   ```

2. **审核响应缓慢**
   ```bash
   # 调整超时配置
   export MODERATION_LLM_TIMEOUT=15000
   ```

3. **误判率过高**
   ```bash
   # 调整严格程度
   export MODERATION_STRICTNESS=loose
   ```

### 日志查看

审核相关日志会显示：
```
[Moderation] Starting content moderation for POST /api/video/submit
[Moderation] Completed in 1250ms, result: APPROVED
[Moderation] Content rejected: violations: ["violence"]
```

## 最佳实践

1. **合理配置严格程度**：根据应用场景选择合适的审核级别
2. **使用预测试**：重要内容提交前先测试
3. **定期查看统计**：监控审核效果和性能
4. **自定义敏感词**：根据业务特点添加行业相关词汇
5. **缓存优化**：合理配置缓存时间，平衡性能和实时性

## 技术支持

如遇问题，请检查：
1. 环境变量配置是否正确
2. API密钥是否有效
3. 网络连接是否正常
4. 服务日志中的错误信息
