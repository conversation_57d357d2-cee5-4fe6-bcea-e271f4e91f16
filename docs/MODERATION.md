# 内容审核系统文档

## 概述

本系统提供了完整的内容审核功能，用于在视频生成请求处理前预先审核文字描述和图片内容，确保生成的内容符合平台规范。

## 功能特性

### 1. 文字内容审核
- **敏感词检测**：基于预定义敏感词库进行快速检测
- **智能审核**：调用大模型进行深度内容分析
- **多维度检测**：暴力、色情、政治敏感、仇恨言论、非法活动等

### 2. 图片内容审核
- **图片下载验证**：验证图片URL有效性和格式
- **视觉内容分析**：使用大模型分析图片内容
- **多格式支持**：支持 JPG、PNG、GIF、WebP 等格式

### 3. 审核配置管理
- **严格程度控制**：支持 strict、normal、loose 三种模式
- **自定义规则**：支持添加自定义审核规则
- **敏感词管理**：支持动态添加/删除敏感词
- **白名单机制**：支持IP和API Key白名单

### 4. 缓存和性能优化
- **结果缓存**：避免重复审核相同内容
- **速率限制**：防止审核服务过载
- **统计监控**：提供详细的审核统计信息

## 配置说明

### 环境变量配置

```bash
# 基础配置
MODERATION_ENABLED=true                    # 是否启用审核
MODERATION_STRICTNESS=normal               # 审核严格程度

# 大模型配置
MODERATION_LLM_URL=https://api.openai.com/v1/chat/completions
MODERATION_LLM_MODEL=gpt-4o-mini
MODERATION_LLM_API_KEY=your-api-key-here
MODERATION_LLM_TIMEOUT=10000

# 图片审核配置
IMAGE_MODERATION_ENABLED=true
IMAGE_DOWNLOAD_TIMEOUT=15000
IMAGE_MAX_SIZE=10485760

# 缓存配置
MODERATION_CACHE_ENABLED=true
MODERATION_CACHE_TTL=3600
```

### 审核严格程度

| 模式 | 阈值 | 描述 |
|------|------|------|
| strict | 0.3 | 严格模式，对可疑内容零容忍 |
| normal | 0.6 | 正常模式，平衡安全性和可用性 |
| loose | 0.8 | 宽松模式，仅拦截明显违规内容 |

## API 接口

### 1. 内容审核测试

```http
POST /api/moderation/test
Content-Type: application/json

{
  "text": "要审核的文字内容",
  "imageUrl": "https://example.com/image.jpg"
}
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "approved": false,
    "reason": "Content moderation failed",
    "violations": ["violence", "inappropriate"],
    "suggestions": "请移除暴力内容并使用更合适的表达方式",
    "details": [
      {
        "type": "text",
        "approved": false,
        "confidence": 0.2,
        "violations": ["violence"]
      }
    ]
  }
}
```

### 2. 审核统计信息

```http
GET /api/moderation/stats
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "totalRequests": 1250,
    "approvedRequests": 1100,
    "rejectedRequests": 150,
    "cacheHits": 300,
    "cacheSize": 45,
    "approvalRate": "88.00%",
    "config": {
      "enabled": true,
      "strictness": "normal"
    }
  }
}
```

### 3. 规则管理

```http
GET /api/moderation/rules
```

**添加敏感词：**
```http
POST /api/moderation/sensitive-words
Content-Type: application/json

{
  "category": "custom",
  "words": ["新敏感词1", "新敏感词2"]
}
```

**删除敏感词：**
```http
DELETE /api/moderation/sensitive-words
Content-Type: application/json

{
  "category": "custom",
  "words": ["要删除的词"]
}
```

## 使用示例

### 1. 基本使用

系统会自动对所有视频生成请求进行审核：

```javascript
// 原有的视频生成请求
const response = await fetch('/api/video/submit', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer your-token'
  },
  body: JSON.stringify({
    prompt: "一个美丽的风景视频",
    images: ["https://example.com/image.jpg"],
    model: "veo2"
  })
});

// 如果审核不通过，会返回 400 错误
if (!response.ok) {
  const error = await response.json();
  if (error.code === 'CONTENT_MODERATION_FAILED') {
    console.log('审核失败:', error.details.suggestions);
  }
}
```

### 2. 预先测试内容

在提交正式请求前，可以先测试内容是否能通过审核：

```javascript
const testResponse = await fetch('/api/moderation/test', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    text: "要测试的文字内容",
    imageUrl: "https://example.com/test-image.jpg"
  })
});

const testResult = await testResponse.json();
if (testResult.data.approved) {
  // 内容通过审核，可以提交正式请求
  console.log('内容审核通过');
} else {
  // 内容未通过审核，显示建议
  console.log('审核建议:', testResult.data.suggestions);
}
```

### 3. 管理自定义规则

```javascript
// 添加自定义敏感词
await fetch('/api/moderation/sensitive-words', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    category: 'custom',
    words: ['自定义敏感词1', '自定义敏感词2']
  })
});

// 查看当前规则
const rulesResponse = await fetch('/api/moderation/rules');
const rules = await rulesResponse.json();
console.log('当前规则:', rules.data);
```

## 错误处理

### 审核失败响应

当内容审核失败时，系统返回 400 状态码：

```json
{
  "success": false,
  "error": "Content moderation failed",
  "code": "CONTENT_MODERATION_FAILED",
  "details": {
    "reason": "Content contains inappropriate material",
    "violations": ["violence", "inappropriate"],
    "suggestions": "请移除不当内容并使用更合适的表达方式",
    "rejectedCount": 1,
    "totalCount": 2
  },
  "timestamp": "2025-07-09T08:30:00.000Z"
}
```

### 审核服务错误

当审核服务不可用时，根据配置可能允许请求通过或拒绝请求：

```json
{
  "success": false,
  "error": "Content moderation service unavailable",
  "code": "MODERATION_SERVICE_ERROR",
  "details": {
    "reason": "Unable to verify content safety",
    "suggestions": "请稍后重试"
  }
}
```

## 最佳实践

1. **配置合适的严格程度**：根据业务需求选择合适的审核严格程度
2. **使用预测试**：在正式提交前使用测试接口验证内容
3. **监控审核统计**：定期查看审核统计，调整规则配置
4. **自定义敏感词**：根据业务特点添加行业相关的敏感词
5. **缓存优化**：合理配置缓存时间，平衡性能和实时性

## 故障排除

### 常见问题

1. **审核服务启动失败**
   - 检查 `MODERATION_LLM_API_KEY` 是否正确配置
   - 确认网络连接正常

2. **审核响应缓慢**
   - 检查大模型API响应时间
   - 考虑调整 `MODERATION_LLM_TIMEOUT` 配置

3. **误判率过高**
   - 调整审核严格程度为 `loose`
   - 添加白名单词汇

4. **缓存问题**
   - 使用 `/api/moderation/clear-cache` 清理缓存
   - 检查缓存配置是否合理
