version: '3.8'

services:
  # 生产环境服务
  video-proxy:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: video-proxy-server
    ports:
      - "${PORT:-8847}:8847"
    environment:
      - NODE_ENV=production
      - PORT=8847
    env_file:
      - .env
    restart: unless-stopped
    networks:
      - video-proxy-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:8847/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 开发环境服务
  video-proxy-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: dev
    container_name: video-proxy-server-dev
    ports:
      - "${DEV_PORT:-8848}:8847"
    environment:
      - NODE_ENV=development
      - PORT=8847
    env_file:
      - .env
    volumes:
      - .:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - video-proxy-network
    profiles:
      - dev

networks:
  video-proxy-network:
    driver: bridge 