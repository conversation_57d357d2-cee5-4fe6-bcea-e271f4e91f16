#!/usr/bin/env node

/**
 * 多语言审核测试脚本
 */

// 加载环境变量
require('dotenv').config();

const fetch = require('node-fetch');

const SERVER_URL = 'http://localhost:8847';

/**
 * 测试多语言审核功能
 */
async function testMultilingualModeration() {
    console.log('🌍 多语言审核功能测试');
    console.log('='.repeat(50));

    const testCases = [
        {
            name: '中文内容测试',
            language: 'zh',
            data: {
                text: '一个美丽的日落场景，金色的阳光洒在宁静的湖面上'
            },
            expectedLanguage: 'zh'
        },
        {
            name: '英文内容测试',
            language: 'en',
            data: {
                text: 'A beautiful sunset scene with golden sunlight reflecting on a peaceful lake'
            },
            expectedLanguage: 'en'
        },
        {
            name: '中文违规内容测试',
            language: 'zh',
            data: {
                text: '一个血腥的战斗场面，到处都是暴力和死亡'
            },
            expectedLanguage: 'zh'
        },
        {
            name: '英文违规内容测试',
            language: 'en',
            data: {
                text: 'A violent battle scene with blood and death everywhere'
            },
            expectedLanguage: 'en'
        },
        {
            name: '混合语言内容测试',
            language: 'mixed',
            data: {
                text: 'This is a beautiful 美丽的风景 landscape video'
            },
            expectedLanguage: 'zh' // 应该检测为中文因为包含中文字符
        }
    ];

    for (const testCase of testCases) {
        console.log(`\n📝 ${testCase.name}`);
        console.log(`内容: ${testCase.data.text}`);
        console.log(`预期语言: ${testCase.expectedLanguage}`);

        try {
            const response = await fetch(`${SERVER_URL}/api/moderation/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testCase.data)
            });

            const result = await response.json();

            if (result.success) {
                const moderationResult = result.data;
                console.log(`审核结果: ${moderationResult.approved ? '✅ 通过' : '❌ 不通过'}`);
                
                if (!moderationResult.approved) {
                    console.log(`违规类型: ${moderationResult.violations?.join(', ') || '未知'}`);
                    console.log(`改进建议: ${moderationResult.suggestions || '无'}`);
                    
                    // 检查返回语言是否正确
                    const suggestionsLanguage = detectResponseLanguage(moderationResult.suggestions);
                    const violationsLanguage = detectResponseLanguage(moderationResult.violations?.join(' ') || '');
                    
                    console.log(`建议语言: ${suggestionsLanguage}`);
                    console.log(`违规类型语言: ${violationsLanguage}`);
                    
                    if (testCase.expectedLanguage === 'zh') {
                        if (suggestionsLanguage === 'zh' || violationsLanguage === 'zh') {
                            console.log('✅ 语言检测正确 - 返回中文');
                        } else {
                            console.log('❌ 语言检测错误 - 应返回中文');
                        }
                    } else if (testCase.expectedLanguage === 'en') {
                        if (suggestionsLanguage === 'en' && violationsLanguage === 'en') {
                            console.log('✅ 语言检测正确 - 返回英文');
                        } else {
                            console.log('❌ 语言检测错误 - 应返回英文');
                        }
                    }
                } else {
                    console.log('✅ 内容通过审核');
                }
            } else {
                console.log(`❌ 测试失败: ${result.error}`);
            }

        } catch (error) {
            console.error(`❌ 请求失败: ${error.message}`);
        }
    }
}

/**
 * 简单的响应语言检测
 */
function detectResponseLanguage(text) {
    if (!text) return 'unknown';
    
    // 检查是否包含中文字符
    const chineseRegex = /[\u4e00-\u9fff]/;
    if (chineseRegex.test(text)) {
        return 'zh';
    }
    
    return 'en';
}

/**
 * 测试完整的视频生成请求
 */
async function testVideoGenerationWithLanguages() {
    console.log('\n🎬 视频生成请求多语言测试');
    console.log('='.repeat(50));

    const testRequests = [
        {
            name: '中文视频请求',
            data: {
                messages: [{
                    role: "user",
                    content: [{
                        type: "text",
                        text: "一个美丽的自然风景视频，展示春天的花朵盛开"
                    }]
                }],
                model: "veo3-pro-frames"
            },
            expectedLanguage: 'zh'
        },
        {
            name: '英文视频请求',
            data: {
                messages: [{
                    role: "user",
                    content: [{
                        type: "text",
                        text: "A beautiful nature landscape video showing spring flowers blooming"
                    }]
                }],
                model: "veo3-pro-frames"
            },
            expectedLanguage: 'en'
        }
    ];

    for (const testRequest of testRequests) {
        console.log(`\n📹 ${testRequest.name}`);
        
        try {
            const response = await fetch(`${SERVER_URL}/api/video/submit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer test-key'
                },
                body: JSON.stringify(testRequest.data)
            });

            const result = await response.json();

            if (response.ok) {
                console.log('✅ 请求通过审核并提交成功');
            } else {
                console.log('❌ 请求被审核拒绝');
                if (result.code === 'CONTENT_MODERATION_FAILED') {
                    console.log(`错误信息: ${result.error}`);
                    console.log(`建议: ${result.details.suggestions}`);
                    
                    // 检查错误信息语言
                    const errorLanguage = detectResponseLanguage(result.error);
                    const suggestionsLanguage = detectResponseLanguage(result.details.suggestions);
                    
                    console.log(`错误信息语言: ${errorLanguage}`);
                    console.log(`建议语言: ${suggestionsLanguage}`);
                    
                    if (testRequest.expectedLanguage === errorLanguage || testRequest.expectedLanguage === suggestionsLanguage) {
                        console.log('✅ 错误信息语言正确');
                    } else {
                        console.log('❌ 错误信息语言不匹配');
                    }
                }
            }

        } catch (error) {
            console.error(`❌ 请求失败: ${error.message}`);
        }
    }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
    try {
        await testMultilingualModeration();
        await testVideoGenerationWithLanguages();
        
        console.log('\n✅ 多语言测试完成');
        console.log('\n💡 说明:');
        console.log('- 系统会自动检测输入内容的语言');
        console.log('- 中文内容返回中文审核结果');
        console.log('- 英文内容返回英文审核结果');
        console.log('- 混合语言内容优先使用中文返回');
        console.log('- 无法确定语言时默认使用英文');
        
    } catch (error) {
        console.error('测试执行失败:', error);
    }
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
    runAllTests();
}

module.exports = {
    testMultilingualModeration,
    testVideoGenerationWithLanguages,
    runAllTests
};
