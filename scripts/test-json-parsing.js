#!/usr/bin/env node

/**
 * JSON解析功能测试脚本
 */

// 加载环境变量
require('dotenv').config();

const moderationService = require('../services/moderationService');

/**
 * 测试JSON解析功能
 */
function testJsonParsing() {
    console.log('🔍 JSON解析功能测试');
    console.log('='.repeat(50));

    const testCases = [
        {
            name: '标准JSON格式',
            response: `{
  "approved": true,
  "confidence": 0.9,
  "violations": [],
  "suggestions": "Content looks good",
  "reason": "No violations found"
}`,
            shouldPass: true
        },
        {
            name: 'Markdown代码块格式',
            response: `\`\`\`json
{
  "approved": false,
  "confidence": 0.3,
  "violations": ["violence"],
  "suggestions": "Remove violent content",
  "reason": "Contains violence"
}
\`\`\``,
            shouldPass: true
        },
        {
            name: '带额外文字的Markdown格式',
            response: `Based on my analysis, here is the moderation result:

\`\`\`json
{
  "approved": false,
  "confidence": 0.2,
  "violations": ["violence", "inappropriate"],
  "suggestions": "Please modify the content",
  "reason": "Multiple violations detected"
}
\`\`\`

This content should be rejected due to policy violations.`,
            shouldPass: true
        },
        {
            name: '不带json标记的代码块',
            response: `\`\`\`
{
  "approved": true,
  "confidence": 0.8,
  "violations": [],
  "suggestions": "",
  "reason": "Content approved"
}
\`\`\``,
            shouldPass: true
        },
        {
            name: '混合格式（前后有文字）',
            response: `The content has been reviewed.

{
  "approved": false,
  "confidence": 0.4,
  "violations": ["hate"],
  "suggestions": "Remove hate speech",
  "reason": "Contains hate speech"
}

Please make the necessary changes.`,
            shouldPass: true
        },
        {
            name: '格式错误的JSON',
            response: `{
  "approved": false,
  "confidence": 0.5,
  "violations": ["violence"],
  "suggestions": "Fix content",
  "reason": "Bad content"
  // 这是一个注释，会导致JSON解析失败
}`,
            shouldPass: false
        },
        {
            name: '完全无效的响应',
            response: `This is not JSON at all. Just plain text response without any structured data.`,
            shouldPass: false
        },
        {
            name: '包含拒绝关键词的文本响应',
            response: `The content violates our policy and should be rejected. It contains inappropriate material that is not compliant with guidelines.`,
            shouldPass: true, // 应该通过降级分析
            expectFallback: true
        },
        {
            name: '包含通过关键词的文本响应',
            response: `The content looks good and is approved. It complies with all policies and is appropriate for video generation.`,
            shouldPass: true, // 应该通过降级分析
            expectFallback: true
        }
    ];

    let passedTests = 0;
    let totalTests = testCases.length;

    for (const testCase of testCases) {
        console.log(`\n📝 测试: ${testCase.name}`);
        console.log(`响应内容: ${testCase.response.substring(0, 100)}${testCase.response.length > 100 ? '...' : ''}`);

        try {
            const result = moderationService.parseJsonFromResponse(testCase.response);

            if (testCase.shouldPass) {
                console.log('✅ 解析成功');
                console.log(`结果: approved=${result.approved}, confidence=${result.confidence}`);
                console.log(`违规类型: ${result.violations?.join(', ') || '无'}`);

                if (testCase.expectFallback && result.fallbackAnalysis) {
                    console.log('🔄 使用了降级分析（预期）');
                } else if (testCase.expectFallback && !result.fallbackAnalysis) {
                    console.log('⚠️  预期使用降级分析但没有使用');
                } else if (!testCase.expectFallback && result.fallbackAnalysis) {
                    console.log('⚠️  意外使用了降级分析');
                }

                passedTests++;
            } else {
                console.log('❌ 预期解析失败但成功了');
                console.log(`意外结果: ${JSON.stringify(result, null, 2)}`);
            }

        } catch (error) {
            if (!testCase.shouldPass) {
                console.log('✅ 预期解析失败，结果正确');
                console.log(`错误: ${error.message}`);
                passedTests++;
            } else {
                console.log('❌ 解析失败');
                console.log(`错误: ${error.message}`);
            }
        }
    }

    console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！JSON解析功能正常');
    } else {
        console.log('⚠️  部分测试失败，需要检查JSON解析逻辑');
    }

    return passedTests === totalTests;
}

/**
 * 测试实际的LLM响应解析
 */
async function testRealLLMResponse() {
    console.log('\n🤖 实际LLM响应测试');
    console.log('='.repeat(50));

    // 模拟一些真实的LLM响应格式
    const realResponses = [
        {
            name: 'GPT-4典型响应',
            response: `I'll analyze this content for moderation:

\`\`\`json
{
  "approved": false,
  "confidence": 0.85,
  "violations": ["暴力和危险内容"],
  "suggestions": "可以考虑将内容调整为更积极的主题，避免涉及战斗和暴力的场景。",
  "reason": "内容涉及战斗的后果，虽然没有极端暴力或血腥场面，但仍然可能引发对暴力的联想，违反了Google Veo的内容政策。",
  "veo_policy_compliance": "不符合Google Veo内容政策"
}
\`\`\``
        },
        {
            name: 'Claude典型响应',
            response: `Based on Google Veo content policies, here's my assessment:

\`\`\`json
{
  "approved": true,
  "confidence": 0.92,
  "violations": [],
  "suggestions": "",
  "reason": "Content describes a peaceful landscape scene which complies with all Google Veo policies",
  "veo_policy_compliance": "compliant"
}
\`\`\`

The content appears to be appropriate for video generation.`
        },
        {
            name: '格式不规范的响应',
            response: `\`\`\`
{
"approved":false,
"confidence":0.3,
"violations":["violence","inappropriate"],
"suggestions":"Remove violent elements",
"reason":"Multiple policy violations"
}
\`\`\``
        }
    ];

    for (const testResponse of realResponses) {
        console.log(`\n🔍 测试: ${testResponse.name}`);
        
        try {
            const result = moderationService.parseJsonFromResponse(testResponse.response);
            console.log('✅ 解析成功');
            console.log(`审核结果: ${result.approved ? '通过' : '不通过'}`);
            console.log(`置信度: ${result.confidence}`);
            if (result.violations && result.violations.length > 0) {
                console.log(`违规类型: ${result.violations.join(', ')}`);
            }
            if (result.suggestions) {
                console.log(`建议: ${result.suggestions.substring(0, 50)}${result.suggestions.length > 50 ? '...' : ''}`);
            }
        } catch (error) {
            console.log('❌ 解析失败');
            console.log(`错误: ${error.message}`);
        }
    }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
    console.log('🧪 JSON解析兼容性测试');
    console.log('='.repeat(60));
    
    const basicTestsPassed = testJsonParsing();
    await testRealLLMResponse();
    
    console.log('\n✅ 测试完成');
    console.log('\n💡 说明:');
    console.log('- 系统现在支持多种JSON响应格式');
    console.log('- 兼容标准JSON和Markdown代码块格式');
    console.log('- 自动清理常见的格式问题');
    console.log('- 提供详细的错误信息用于调试');
    
    if (basicTestsPassed) {
        console.log('\n🎯 建议: JSON解析功能已优化，可以处理大模型的各种响应格式');
    } else {
        console.log('\n⚠️  警告: 部分测试失败，建议检查解析逻辑');
    }
}

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
    runAllTests().catch(console.error);
}

module.exports = {
    testJsonParsing,
    testRealLLMResponse,
    runAllTests
};
