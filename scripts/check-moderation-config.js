#!/usr/bin/env node

/**
 * 内容审核配置检查工具
 */

// 加载环境变量
require('dotenv').config();

const { MODERATION_CONFIG } = require('../config/moderation');

console.log('🔍 内容审核配置检查');
console.log('='.repeat(50));

// 检查基础配置
console.log('\n📋 基础配置:');
console.log(`✓ 审核启用状态: ${MODERATION_CONFIG.enabled ? '✅ 启用' : '❌ 禁用'}`);
console.log(`✓ 审核严格程度: ${MODERATION_CONFIG.strictness}`);
console.log(`✓ 文本审核: ${MODERATION_CONFIG.text.enabled ? '✅ 启用' : '❌ 禁用'}`);
console.log(`✓ 图片审核: ${MODERATION_CONFIG.image.enabled ? '✅ 启用' : '❌ 禁用'}`);
console.log(`✓ 缓存功能: ${MODERATION_CONFIG.cache.enabled ? '✅ 启用' : '❌ 禁用'}`);

// 检查LLM配置
console.log('\n🤖 LLM配置:');
console.log(`✓ API地址: ${MODERATION_CONFIG.llm.url}`);
console.log(`✓ 模型名称: ${MODERATION_CONFIG.llm.model}`);
console.log(`✓ API密钥: ${MODERATION_CONFIG.llm.apiKey ? '✅ 已配置' : '❌ 未配置'}`);
console.log(`✓ 超时时间: ${MODERATION_CONFIG.llm.timeout}ms`);
console.log(`✓ 重试次数: ${MODERATION_CONFIG.llm.maxRetries}`);

// 检查环境变量
console.log('\n🌍 环境变量:');
const envVars = [
    'MODERATION_ENABLED',
    'MODERATION_STRICTNESS', 
    'MODERATION_LLM_URL',
    'MODERATION_LLM_MODEL',
    'MODERATION_LLM_API_KEY',
    'MODERATION_LLM_TIMEOUT',
    'IMAGE_MODERATION_ENABLED',
    'MODERATION_CACHE_ENABLED'
];

envVars.forEach(varName => {
    const value = process.env[varName];
    const status = value ? '✅ 已设置' : '⚠️  未设置(使用默认值)';
    console.log(`✓ ${varName}: ${status}`);
    if (value) {
        console.log(`   值: ${varName.includes('API_KEY') ? '***隐藏***' : value}`);
    }
});

// 配置建议
console.log('\n💡 配置建议:');

if (!MODERATION_CONFIG.llm.apiKey) {
    console.log('❌ 缺少LLM API密钥配置');
    console.log('   解决方案:');
    console.log('   1. 获取OpenAI API密钥: https://platform.openai.com/api-keys');
    console.log('   2. 设置环境变量: export MODERATION_LLM_API_KEY="your-key-here"');
    console.log('   3. 或在.env文件中添加: MODERATION_LLM_API_KEY=your-key-here');
    console.log('   4. 重启服务');
}

if (!MODERATION_CONFIG.enabled) {
    console.log('⚠️  内容审核已禁用');
    console.log('   如需启用，设置: MODERATION_ENABLED=true');
}

if (MODERATION_CONFIG.strictness === 'loose') {
    console.log('⚠️  当前使用宽松审核模式');
    console.log('   对于生产环境，建议使用: MODERATION_STRICTNESS=normal 或 strict');
}

// 测试连接
console.log('\n🔗 连接测试:');

async function testLLMConnection() {
    if (!MODERATION_CONFIG.llm.apiKey) {
        console.log('❌ 无法测试LLM连接 - API密钥未配置');
        return;
    }

    try {
        const fetch = require('node-fetch');
        
        console.log('🔄 测试LLM API连接...');
        
        const response = await fetch(MODERATION_CONFIG.llm.url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${MODERATION_CONFIG.llm.apiKey}`
            },
            body: JSON.stringify({
                model: MODERATION_CONFIG.llm.model,
                messages: [{ role: 'user', content: 'test' }],
                max_tokens: 10
            }),
            timeout: 5000
        });

        if (response.ok) {
            console.log('✅ LLM API连接成功');
        } else {
            console.log(`❌ LLM API连接失败: ${response.status} ${response.statusText}`);
            
            if (response.status === 401) {
                console.log('   可能原因: API密钥无效');
            } else if (response.status === 429) {
                console.log('   可能原因: API配额不足或请求频率过高');
            } else if (response.status === 404) {
                console.log('   可能原因: API地址或模型名称错误');
            }
        }
    } catch (error) {
        console.log(`❌ LLM API连接测试失败: ${error.message}`);
        
        if (error.code === 'ENOTFOUND') {
            console.log('   可能原因: 网络连接问题或API地址错误');
        } else if (error.code === 'ETIMEDOUT') {
            console.log('   可能原因: 请求超时，网络较慢');
        }
    }
}

// 运行测试
testLLMConnection().then(() => {
    console.log('\n📊 配置检查完成');
    
    // 总结
    const issues = [];
    if (!MODERATION_CONFIG.llm.apiKey) issues.push('LLM API密钥未配置');
    if (!MODERATION_CONFIG.enabled) issues.push('内容审核已禁用');
    
    if (issues.length === 0) {
        console.log('✅ 配置检查通过，系统可以正常运行');
    } else {
        console.log(`⚠️  发现 ${issues.length} 个配置问题:`);
        issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue}`);
        });
    }
    
    console.log('\n🚀 启动建议:');
    if (MODERATION_CONFIG.llm.apiKey) {
        console.log('   配置完整，可以启动完整的内容审核功能');
        console.log('   运行: npm start');
    } else {
        console.log('   可以启动基础审核功能（仅敏感词检测）');
        console.log('   运行: npm start');
        console.log('   配置LLM API密钥后可启用完整的智能审核功能');
    }
}).catch(console.error);
