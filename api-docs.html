<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-zh="视频生成API接口文档" data-en="Video Generation API Documentation">Video Generation API Documentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: #f8f9fa;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 0;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 10px;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .lang-switch {
            position: absolute;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }

        .lang-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .lang-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .lang-btn.active {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
        }

        .nav {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            position: sticky;
            top: 20px;
            z-index: 100;
        }

        .nav ul {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }

        .nav a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .nav a:hover {
            background: #667eea;
            color: white;
        }

        .section {
            background: white;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .section-header h2 {
            color: #495057;
            font-size: 1.8em;
            margin-bottom: 10px;
        }

        .section-content {
            padding: 30px;
        }

        .endpoint {
            margin-bottom: 40px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .endpoint-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }

        .endpoint-title {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 10px;
        }

        .method {
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9em;
            text-transform: uppercase;
        }

        .method.post {
            background: #28a745;
            color: white;
        }

        .method.get {
            background: #007bff;
            color: white;
        }

        .endpoint-url {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 1.1em;
            color: #495057;
        }

        .endpoint-description {
            color: #6c757d;
            margin-top: 5px;
        }

        .endpoint-content {
            padding: 20px;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .param-table th,
        .param-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .param-table th {
            background: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .param-required {
            color: #dc3545;
            font-weight: bold;
        }

        .param-optional {
            color: #28a745;
            font-weight: bold;
        }

        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            white-space: pre-wrap;
        }

        .response-example {
            margin: 20px 0;
        }

        .response-example h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .status-code {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 3px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .status-200 {
            background: #d4edda;
            color: #155724;
        }

        .status-400 {
            background: #f8d7da;
            color: #721c24;
        }

        .status-401 {
            background: #fff3cd;
            color: #856404;
        }

        .status-500 {
            background: #f5c6cb;
            color: #721c24;
        }

        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }

        .info-box.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
        }

        .info-box.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }

        .info-box h4 {
            color: #0c5460;
            margin-bottom: 10px;
        }

        .info-box.warning h4 {
            color: #856404;
        }

        .info-box.error h4 {
            color: #721c24;
        }

        .copy-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            float: right;
            margin-top: -10px;
        }

        .copy-btn:hover {
            background: #5a6268;
        }

        .model-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .model-card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            background: white;
        }

        .model-card h4 {
            color: #495057;
            margin-bottom: 10px;
        }

        .model-card .model-name {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9em;
        }

        /* 隐藏非当前语言的内容 */
        [data-lang]:not([data-lang="en"]) {
            display: none;
        }

        body.lang-en [data-lang]:not([data-lang="en"]) {
            display: none;
        }

        body.lang-en [data-lang="en"] {
            display: block;
        }

        body.lang-zh [data-lang]:not([data-lang="zh"]) {
            display: none;
        }

        body.lang-zh [data-lang="zh"] {
            display: block;
        }

        @media (max-width: 768px) {
            .nav ul {
                flex-direction: column;
                gap: 10px;
            }

            .endpoint-title {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .param-table {
                font-size: 0.9em;
            }

            .code-block {
                font-size: 0.8em;
                padding: 15px;
            }

            .lang-switch {
                position: static;
                justify-content: center;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body class="lang-en">
    <div class="container">
        <div class="header">
            <div class="lang-switch">
                <button class="lang-btn" onclick="switchLang('zh')">中文</button>
                <button class="lang-btn active" onclick="switchLang('en')">English</button>
            </div>
            <h1>
                <span data-lang="zh">🎬 视频生成API接口文档</span>
                <span data-lang="en">🎬 Video Generation API Documentation</span>
            </h1>
            <p>
                <span data-lang="zh">强大的异步视频生成服务 - 简单易用的API接口</span>
                <span data-lang="en">Powerful Async Video Generation Service - Simple and Easy-to-use API</span>
            </p>
        </div>

        <nav class="nav">
            <ul>
                <li><a href="#overview" data-lang="zh">概述</a><a href="#overview" data-lang="en">Overview</a></li>
                <li><a href="#authentication" data-lang="zh">认证</a><a href="#authentication" data-lang="en">Authentication</a></li>
                <li><a href="#api-endpoints" data-lang="zh">API接口</a><a href="#api-endpoints" data-lang="en">API Endpoints</a></li>
                <li><a href="#models" data-lang="zh">支持的模型</a><a href="#models" data-lang="en">Supported Models</a></li>
                <li><a href="#examples" data-lang="zh">示例代码</a><a href="#examples" data-lang="en">Code Examples</a></li>
                <li><a href="#errors" data-lang="zh">错误处理</a><a href="#errors" data-lang="en">Error Handling</a></li>
            </ul>
        </nav>

        <section id="overview" class="section">
            <div class="section-header">
                <h2>
                    <span data-lang="zh">📋 API概述</span>
                    <span data-lang="en">📋 API Overview</span>
                </h2>
            </div>
            <div class="section-content">
                <p data-lang="zh">本API提供异步视频生成服务，支持多种AI模型。服务采用提交-查询的异步模式，确保高效处理大量视频生成请求。</p>
                <p data-lang="en">This API provides asynchronous video generation services, supporting multiple AI models. The service uses a submit-query asynchronous mode to ensure efficient processing of large-scale video generation requests.</p>

                <div class="info-box">
                    <h4>
                        <span data-lang="zh">🌐 服务地址</span>
                        <span data-lang="en">🌐 Service URL</span>
                    </h4>
                    <p data-lang="zh"><strong>生产环境：</strong> <code>http://your-domain.com:8847</code></p>
                    <p data-lang="zh"><strong>开发环境：</strong> <code>http://localhost:8847</code></p>
                    <p data-lang="en"><strong>Production:</strong> <code>http://your-domain.com:8847</code></p>
                    <p data-lang="en"><strong>Development:</strong> <code>http://localhost:8847</code></p>
                </div>

                <div class="info-box warning">
                    <h4>
                        <span data-lang="zh">⚡ 重要特性</span>
                        <span data-lang="en">⚡ Key Features</span>
                    </h4>
                    <ul data-lang="zh">
                        <li>异步处理：提交任务后立即返回任务ID，通过轮询获取结果</li>
                        <li>多模型支持：支持VEO2、VEO3等多种视频生成模型</li>
                        <li>简单易用：只需两个接口即可完成视频生成</li>
                        <li>高可用性：内置健康检查和错误重试机制</li>
                    </ul>
                    <ul data-lang="en">
                        <li>Asynchronous Processing: Returns task ID immediately after submission, get results through polling</li>
                        <li>Multi-model Support: Supports various video generation models like VEO2, VEO3</li>
                        <li>Simple to Use: Only two interfaces needed to complete video generation</li>
                        <li>High Availability: Built-in health checks and error retry mechanisms</li>
                    </ul>
                </div>
            </div>
        </section>

        <section id="authentication" class="section">
            <div class="section-header">
                <h2>
                    <span data-lang="zh">🔐 认证方式</span>
                    <span data-lang="en">🔐 Authentication</span>
                </h2>
            </div>
            <div class="section-content">
                <p data-lang="zh">所有API请求都需要在请求头中包含有效的Authorization令牌：</p>
                <p data-lang="en">All API requests require a valid Authorization token in the request header:</p>

                <div class="code-block">
                    <button class="copy-btn" onclick="copyCode(this)" data-lang="zh">复制</button>
                    <button class="copy-btn" onclick="copyCode(this)" data-lang="en">Copy</button>
                    <pre>Authorization: Bearer sk-your-api-key-here</pre>
                </div>

                <div class="info-box error">
                    <h4>
                        <span data-lang="zh">🚨 安全提醒</span>
                        <span data-lang="en">🚨 Security Notice</span>
                    </h4>
                    <p data-lang="zh">请妥善保管您的API密钥，不要在客户端代码中暴露。建议在服务端环境变量中存储密钥。</p>
                    <p data-lang="en">Please keep your API key secure and do not expose it in client-side code. It's recommended to store the key in server-side environment variables.</p>
                </div>
            </div>
        </section>

        <section id="api-endpoints" class="section">
            <div class="section-header">
                <h2>
                    <span data-lang="zh">🚀 API接口</span>
                    <span data-lang="en">🚀 API Endpoints</span>
                </h2>
            </div>
            <div class="section-content">
                <p data-lang="zh">我们提供两个简单的API接口来完成视频生成：</p>
                <p data-lang="en">We provide two simple API endpoints to complete video generation:</p>

                <div class="endpoint">
                    <div class="endpoint-header">
                        <div class="endpoint-title">
                            <span class="method post">POST</span>
                            <span class="endpoint-url">/api/video/submit</span>
                        </div>
                        <div class="endpoint-description">
                            <span data-lang="zh">提交视频生成任务</span>
                            <span data-lang="en">Submit video generation task</span>
                        </div>
                    </div>
                    <div class="endpoint-content">
                        <h4>
                            <span data-lang="zh">请求参数</span>
                            <span data-lang="en">Request Parameters</span>
                        </h4>
                        <table class="param-table">
                            <thead>
                                <tr data-lang="zh">
                                    <th>参数名</th>
                                    <th>类型</th>
                                    <th>必填</th>
                                    <th>说明</th>
                                </tr>
                                <tr data-lang="en">
                                    <th>Parameter</th>
                                    <th>Type</th>
                                    <th>Required</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>prompt</code></td>
                                    <td>string</td>
                                    <td><span class="param-required" data-lang="zh">必填</span><span class="param-required" data-lang="en">Required</span></td>
                                    <td data-lang="zh">视频生成的文本描述</td>
                                    <td data-lang="en">Text description for video generation</td>
                                </tr>
                                <tr>
                                    <td><code>model</code></td>
                                    <td>string</td>
                                    <td><span class="param-optional" data-lang="zh">可选</span><span class="param-optional" data-lang="en">Optional</span></td>
                                    <td data-lang="zh">使用的模型名称，默认为 "veo3"</td>
                                    <td data-lang="en">Model name to use, defaults to "veo3"</td>
                                </tr>
                                <tr>
                                    <td><code>images</code></td>
                                    <td>array</td>
                                    <td><span class="param-optional" data-lang="zh">可选</span><span class="param-optional" data-lang="en">Optional</span></td>
                                    <td data-lang="zh">参考图片URL数组，用于辅助视频生成</td>
                                    <td data-lang="en">Array of reference image URLs for video generation assistance</td>
                                </tr>
                                <tr>
                                    <td><code>enhance_prompt</code></td>
                                    <td>boolean</td>
                                    <td><span class="param-optional" data-lang="zh">可选</span><span class="param-optional" data-lang="en">Optional</span></td>
                                    <td data-lang="zh">是否增强提示词，默认为 false</td>
                                    <td data-lang="en">Whether to enhance prompt, defaults to false</td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="info-box">
                            <h4>
                                <span data-lang="zh">📸 关于图片参数</span>
                                <span data-lang="en">📸 About Images Parameter</span>
                            </h4>
                            <ul data-lang="zh">
                                <li>支持多张参考图片，以数组形式提供图片URL</li>
                                <li>图片将用于辅助AI理解视频生成需求</li>
                                <li>支持常见图片格式：JPG、PNG、WebP等</li>
                                <li>建议图片尺寸不超过2048x2048像素</li>
                            </ul>
                            <ul data-lang="en">
                                <li>Supports multiple reference images provided as an array of image URLs</li>
                                <li>Images will help AI understand video generation requirements</li>
                                <li>Supports common image formats: JPG, PNG, WebP, etc.</li>
                                <li>Recommended image size not exceeding 2048x2048 pixels</li>
                            </ul>
                        </div>

                        <h4>
                            <span data-lang="zh">请求示例</span>
                            <span data-lang="en">Request Example</span>
                        </h4>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)" data-lang="zh">复制</button>
                            <button class="copy-btn" onclick="copyCode(this)" data-lang="en">Copy</button>
                            <pre>curl --location --request POST 'http://localhost:8847/api/video/submit' \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-api-key-here" \
  -d '{
    "prompt": "a cat fly in the sky",
    "model": "veo3",
    "images": [
        "https://filesystem.site/cdn/20250612/VfgB5ubjInVt8sG6rzMppxnu7gEfde.png",
        "https://filesystem.site/cdn/20250612/998IGmUiM2koBGZM3UnZeImbPBNIUL.png"
    ],
    "enhance_prompt": true
  }'</pre>
                        </div>

                        <h4>
                            <span data-lang="zh">基础请求示例（不含图片）</span>
                            <span data-lang="en">Basic Request Example (without images)</span>
                        </h4>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)" data-lang="zh">复制</button>
                            <button class="copy-btn" onclick="copyCode(this)" data-lang="en">Copy</button>
                            <pre>curl --location --request POST 'http://localhost:8847/api/video/submit' \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-api-key-here" \
  -d '{
    "prompt": "a cat fly in the sky",
    "model": "veo3",
    "enhance_prompt": true
  }'</pre>
                        </div>

                        <div class="response-example">
                            <h4>
                                <span data-lang="zh">响应示例</span>
                                <span data-lang="en">Response Example</span>
                                <span class="status-code status-200">200 OK</span>
                            </h4>
                            <div class="code-block">
                                <button class="copy-btn" onclick="copyCode(this)" data-lang="zh">复制</button>
                                <button class="copy-btn" onclick="copyCode(this)" data-lang="en">Copy</button>
                                <pre>{
  "success": true,
  "data": {
    "taskId": "veo3:0ff04a94-6c7e-4f86-af66-3adb62214eb2",
    "pollingUrl": "https://asyncdata.net/source/veo3:0ff04a94-6c7e-4f86-af66-3adb62214eb2",
    "status": "processing",
    "message": "Task submitted successfully"
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="endpoint">
                    <div class="endpoint-header">
                        <div class="endpoint-title">
                            <span class="method get">GET</span>
                            <span class="endpoint-url">/api/video/status/{taskId}</span>
                        </div>
                        <div class="endpoint-description">
                            <span data-lang="zh">查询视频生成状态</span>
                            <span data-lang="en">Query video generation status</span>
                        </div>
                    </div>
                    <div class="endpoint-content">
                        <h4>
                            <span data-lang="zh">路径参数</span>
                            <span data-lang="en">Path Parameters</span>
                        </h4>
                        <table class="param-table">
                            <thead>
                                <tr data-lang="zh">
                                    <th>参数名</th>
                                    <th>类型</th>
                                    <th>说明</th>
                                </tr>
                                <tr data-lang="en">
                                    <th>Parameter</th>
                                    <th>Type</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><code>taskId</code></td>
                                    <td>string</td>
                                    <td data-lang="zh">任务ID（从提交接口返回）</td>
                                    <td data-lang="en">Task ID (returned from submit endpoint)</td>
                                </tr>
                            </tbody>
                        </table>

                        <h4>
                            <span data-lang="zh">请求示例</span>
                            <span data-lang="en">Request Example</span>
                        </h4>
                        <div class="code-block">
                            <button class="copy-btn" onclick="copyCode(this)" data-lang="zh">复制</button>
                            <button class="copy-btn" onclick="copyCode(this)" data-lang="en">Copy</button>
                            <pre>curl http://localhost:8847/api/video/status/veo3:0ff04a94-6c7e-4f86-af66-3adb62214eb2</pre>
                        </div>

                        <div class="response-example">
                            <h4>
                                <span data-lang="zh">处理中响应</span>
                                <span data-lang="en">Processing Response</span>
                                <span class="status-code status-200">200 OK</span>
                            </h4>
                            <div class="code-block">
                                <button class="copy-btn" onclick="copyCode(this)" data-lang="zh">复制</button>
                                <button class="copy-btn" onclick="copyCode(this)" data-lang="en">Copy</button>
                                <pre>{
  "success": true,
  "data": {
    "taskId": "veo3:0ff04a94-6c7e-4f86-af66-3adb62214eb2",
    "pollingUrl": "https://asyncdata.net/source/veo3:0ff04a94-6c7e-4f86-af66-3adb62214eb2",
    "status": "processing",
    "progress": {
      "upstreamStatus": "video_generating",
      "videoGenerationStatus": "MEDIA_GENERATION_STATUS_ACTIVE",
      "retryCount": 0,
      "maxRetries": 3
    },
    "upstreamData": { ... }
  }
}</pre>
                            </div>
                        </div>

                        <div class="response-example">
                            <h4>
                                <span data-lang="zh">完成响应</span>
                                <span data-lang="en">Completed Response</span>
                                <span class="status-code status-200">200 OK</span>
                            </h4>
                            <div class="code-block">
                                <button class="copy-btn" onclick="copyCode(this)" data-lang="zh">复制</button>
                                <button class="copy-btn" onclick="copyCode(this)" data-lang="en">Copy</button>
                                <pre>{
  "success": true,
  "data": {
    "taskId": "veo3:0ff04a94-6c7e-4f86-af66-3adb62214eb2",
    "status": "completed",
    "result": {
      "video_url": "https://filesystem.site/cdn/20250629/example.mp4",
      "video_media_id": "media_12345"
    }
  }
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="models" class="section">
            <div class="section-header">
                <h2>
                    <span data-lang="zh">🤖 支持的模型</span>
                    <span data-lang="en">🤖 Supported Models</span>
                </h2>
            </div>
            <div class="section-content">
                <p data-lang="zh">我们支持多种先进的视频生成模型，每种模型都有其独特的特性：</p>
                <p data-lang="en">We support various advanced video generation models, each with unique characteristics:</p>

                <div class="model-grid">
                    <div class="model-card">
                        <h4>
                            <span data-lang="zh">VEO2 (经典)</span>
                            <span data-lang="en">VEO2 (Classic)</span>
                        </h4>
                        <p class="model-name">veo2</p>
                        <p data-lang="zh">经典模型，兼容性最好，适合基础的视频生成需求。</p>
                        <p data-lang="en">Classic model with best compatibility, suitable for basic video generation needs.</p>
                        <ul data-lang="zh">
                            <li>生成速度：中等</li>
                            <li>适用场景：基础应用</li>
                            <li>稳定性：高</li>
                        </ul>
                        <ul data-lang="en">
                            <li>Generation Speed: Medium</li>
                            <li>Use Cases: Basic applications</li>
                            <li>Stability: High</li>
                        </ul>
                    </div>

                    <div class="model-card">
                        <h4>VEO2 Fast</h4>
                        <p class="model-name">veo2-fast</p>
                        <p data-lang="zh">标准快速生成模型，适合大多数视频生成需求。</p>
                        <p data-lang="en">Standard fast generation model, suitable for most video generation needs.</p>
                        <ul data-lang="zh">
                            <li>生成速度：快</li>
                            <li>适用场景：通用视频生成</li>
                            <li>推荐指数：⭐⭐⭐⭐⭐</li>
                        </ul>
                        <ul data-lang="en">
                            <li>Generation Speed: Fast</li>
                            <li>Use Cases: General video generation</li>
                            <li>Recommendation: ⭐⭐⭐⭐⭐</li>
                        </ul>
                    </div>

                    <div class="model-card">
                        <h4>VEO2 Fast Components</h4>
                        <p class="model-name">veo2-fast-components</p>
                        <p data-lang="zh">支持组件化生成的快速模型，可以生成带有特定组件的视频。</p>
                        <p data-lang="en">Fast model supporting component-based generation, can create videos with specific components.</p>
                        <ul data-lang="zh">
                            <li>生成速度：快</li>
                            <li>特色：组件化生成</li>
                            <li>适用场景：复杂场景</li>
                        </ul>
                        <ul data-lang="en">
                            <li>Generation Speed: Fast</li>
                            <li>Feature: Component-based generation</li>
                            <li>Use Cases: Complex scenes</li>
                        </ul>
                    </div>

                    <div class="model-card">
                        <h4>VEO2 Fast Frames</h4>
                        <p class="model-name">veo2-fast-frames</p>
                        <p data-lang="zh">支持首尾帧控制的模型，可以精确控制视频的开始和结束画面。</p>
                        <p data-lang="en">Model supporting first and last frame control, can precisely control video start and end frames.</p>
                        <ul data-lang="zh">
                            <li>生成速度：快</li>
                            <li>特色：首尾帧控制</li>
                            <li>适用场景：过渡动画</li>
                        </ul>
                        <ul data-lang="en">
                            <li>Generation Speed: Fast</li>
                            <li>Feature: First/Last frame control</li>
                            <li>Use Cases: Transition animations</li>
                        </ul>
                    </div>

                    <div class="model-card">
                        <h4>VEO2 Pro</h4>
                        <p class="model-name">veo2-pro</p>
                        <p data-lang="zh">专业级VEO2模型，提供更高质量的视频生成效果。</p>
                        <p data-lang="en">Professional VEO2 model providing higher quality video generation results.</p>
                        <ul data-lang="zh">
                            <li>生成速度：中等</li>
                            <li>特色：专业品质</li>
                            <li>适用场景：高质量需求</li>
                        </ul>
                        <ul data-lang="en">
                            <li>Generation Speed: Medium</li>
                            <li>Feature: Professional quality</li>
                            <li>Use Cases: High-quality requirements</li>
                        </ul>
                    </div>

                    <div class="model-card">
                        <h4>VEO3 (第三代)</h4>
                        <p class="model-name">veo3</p>
                        <p data-lang="zh">第三代视频生成模型，支持更多高级功能。</p>
                        <p data-lang="en">Third-generation video generation model with more advanced features.</p>
                        <ul data-lang="zh">
                            <li>生成速度：中等</li>
                            <li>特色：第三代技术</li>
                            <li>适用场景：高级应用</li>
                        </ul>
                        <ul data-lang="en">
                            <li>Generation Speed: Medium</li>
                            <li>Feature: 3rd generation tech</li>
                            <li>Use Cases: Advanced applications</li>
                        </ul>
                    </div>

                    <div class="model-card">
                        <h4>VEO3 Fast</h4>
                        <p class="model-name">veo3-fast</p>
                        <p data-lang="zh">新一代快速模型，支持音频生成，画质更高。</p>
                        <p data-lang="en">Next-generation fast model with audio generation support and higher quality.</p>
                        <ul data-lang="zh">
                            <li>生成速度：快</li>
                            <li>特色：支持音频</li>
                            <li>画质：更高</li>
                        </ul>
                        <ul data-lang="en">
                            <li>Generation Speed: Fast</li>
                            <li>Feature: Audio support</li>
                            <li>Quality: Higher</li>
                        </ul>
                    </div>

                    <div class="model-card">
                        <h4>VEO3 Pro</h4>
                        <p class="model-name">veo3-pro</p>
                        <p data-lang="zh">第三代专业模型，提供卓越的视频生成质量。</p>
                        <p data-lang="en">Third-generation professional model providing excellent video generation quality.</p>
                        <ul data-lang="zh">
                            <li>生成速度：较慢</li>
                            <li>特色：卓越品质</li>
                            <li>适用场景：专业制作</li>
                        </ul>
                        <ul data-lang="en">
                            <li>Generation Speed: Slower</li>
                            <li>Feature: Excellent quality</li>
                            <li>Use Cases: Professional production</li>
                        </ul>
                    </div>

                    <div class="model-card">
                        <h4>VEO3 Pro Frames</h4>
                        <p class="model-name">veo3-pro-frames</p>
                        <p data-lang="zh">专业级高质量模型，支持帧控制，适合需要极致画质的场景。</p>
                        <p data-lang="en">Professional high-quality model with frame control, suitable for scenarios requiring ultimate image quality.</p>
                        <ul data-lang="zh">
                            <li>生成速度：较慢</li>
                            <li>特色：极致画质+帧控制</li>
                            <li>适用场景：专业制作</li>
                        </ul>
                        <ul data-lang="en">
                            <li>Generation Speed: Slower</li>
                            <li>Feature: Ultimate quality + Frame control</li>
                            <li>Use Cases: Professional production</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="examples" class="section">
            <div class="section-header">
                <h2>
                    <span data-lang="zh">💻 示例代码</span>
                    <span data-lang="en">💻 Code Examples</span>
                </h2>
            </div>
            <div class="section-content">
                <h3>JavaScript/Node.js</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyCode(this)" data-lang="zh">复制</button>
                    <button class="copy-btn" onclick="copyCode(this)" data-lang="en">Copy</button>
                    <pre>const fetch = require('node-fetch');

class VideoClient {
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }

    async submitTask(prompt, model = 'veo3', enhancePrompt = true, images = null) {
        const requestBody = {
            prompt,
            model,
            enhance_prompt: enhancePrompt
        };

        // Add images if provided
        if (images && Array.isArray(images) && images.length > 0) {
            requestBody.images = images;
        }

        const response = await fetch(`${this.baseUrl}/api/video/submit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.apiKey}`
            },
            body: JSON.stringify(requestBody)
        });
        return await response.json();
    }

    async getStatus(taskId) {
        const response = await fetch(`${this.baseUrl}/api/video/status/${taskId}`);
        return await response.json();
    }

    async waitForCompletion(taskId, timeout = 300000) {
        const startTime = Date.now();
        while (Date.now() - startTime < timeout) {
            const result = await this.getStatus(taskId);

            if (result.success && result.data.status === 'completed') {
                return result.data;
            }

            if (result.success && result.data.status === 'failed') {
                throw new Error('Video generation failed');
            }

            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        throw new Error('Timeout waiting for video completion');
    }
}

// Usage example / 使用示例
const client = new VideoClient('http://localhost:8847', 'sk-your-api-key');

async function generateVideo() {
    try {
        // Example with images / 带图片的示例
        const images = [
            'https://filesystem.site/cdn/20250612/VfgB5ubjInVt8sG6rzMppxnu7gEfde.png',
            'https://filesystem.site/cdn/20250612/998IGmUiM2koBGZM3UnZeImbPBNIUL.png'
        ];

        const result = await client.submitTask('a cat fly in the sky', 'veo3', true, images);
        console.log('Task submitted:', result.data.taskId);

        // Wait for completion / 等待完成
        const completed = await client.waitForCompletion(result.data.taskId);
        console.log('Video URL:', completed.result.video_url);

    } catch (error) {
        console.error('Error:', error.message);
    }
}

async function generateVideoBasic() {
    try {
        // Basic example without images / 不带图片的基础示例
        const result = await client.submitTask('a cat fly in the sky', 'veo3', true);
        console.log('Task submitted:', result.data.taskId);

        const completed = await client.waitForCompletion(result.data.taskId);
        console.log('Video URL:', completed.result.video_url);

    } catch (error) {
        console.error('Error:', error.message);
    }
}

generateVideo();</pre>
                </div>

                <h3>Python</h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyCode(this)" data-lang="zh">复制</button>
                    <button class="copy-btn" onclick="copyCode(this)" data-lang="en">Copy</button>
                    <pre>import requests
import time

class VideoClient:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }

    def submit_task(self, prompt, model='veo3', enhance_prompt=True, images=None):
        request_body = {
            'prompt': prompt,
            'model': model,
            'enhance_prompt': enhance_prompt
        }

        # Add images if provided
        if images and isinstance(images, list) and len(images) > 0:
            request_body['images'] = images

        response = requests.post(
            f'{self.base_url}/api/video/submit',
            headers=self.headers,
            json=request_body
        )
        return response.json()

    def get_status(self, task_id):
        response = requests.get(f'{self.base_url}/api/video/status/{task_id}')
        return response.json()

    def wait_for_completion(self, task_id, timeout=300):
        start_time = time.time()
        while time.time() - start_time < timeout:
            result = self.get_status(task_id)

            if result['success'] and result['data']['status'] == 'completed':
                return result['data']

            if result['success'] and result['data']['status'] == 'failed':
                raise Exception('Video generation failed')

            time.sleep(2)
        raise Exception('Timeout waiting for video completion')

# Usage example / 使用示例
client = VideoClient('http://localhost:8847', 'sk-your-api-key')

try:
    # Example with images / 带图片的示例
    images = [
        'https://filesystem.site/cdn/20250612/VfgB5ubjInVt8sG6rzMppxnu7gEfde.png',
        'https://filesystem.site/cdn/20250612/998IGmUiM2koBGZM3UnZeImbPBNIUL.png'
    ]

    result = client.submit_task('a cat fly in the sky', 'veo3', True, images)
    print(f'Task submitted: {result["data"]["taskId"]}')

    # Wait for completion / 等待完成
    completed = client.wait_for_completion(result['data']['taskId'])
    print(f'Video URL: {completed["result"]["video_url"]}')

except Exception as e:
    print(f'Error: {e}')

# Basic example without images / 不带图片的基础示例
try:
    result = client.submit_task('a cat fly in the sky', 'veo3', True)
    print(f'Task submitted: {result["data"]["taskId"]}')

    completed = client.wait_for_completion(result['data']['taskId'])
    print(f'Video URL: {completed["result"]["video_url"]}')

except Exception as e:
    print(f'Error: {e}')</pre>
                </div>

                <h3>
                    <span data-lang="zh">cURL 命令行</span>
                    <span data-lang="en">cURL Command Line</span>
                </h3>
                <div class="code-block">
                    <button class="copy-btn" onclick="copyCode(this)" data-lang="zh">复制</button>
                    <button class="copy-btn" onclick="copyCode(this)" data-lang="en">Copy</button>
                    <pre># 1. Submit task / 提交任务
curl --location --request POST 'http://localhost:8847/api/video/submit' \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-api-key-here" \
  -d '{
    "prompt": "a cat fly in the sky",
    "model": "veo3",
    "enhance_prompt": true
  }'

# 2. Query status / 查询状态（使用返回的taskId）
curl http://localhost:8847/api/video/status/veo3:0ff04a94-6c7e-4f86-af66-3adb62214eb2</pre>
                </div>
            </div>
        </section>

        <section id="errors" class="section">
            <div class="section-header">
                <h2>
                    <span data-lang="zh">❌ 错误处理</span>
                    <span data-lang="en">❌ Error Handling</span>
                </h2>
            </div>
            <div class="section-content">
                <h3>
                    <span data-lang="zh">HTTP状态码</span>
                    <span data-lang="en">HTTP Status Codes</span>
                </h3>
                <table class="param-table">
                    <thead>
                        <tr data-lang="zh">
                            <th>状态码</th>
                            <th>说明</th>
                            <th>常见原因</th>
                        </tr>
                        <tr data-lang="en">
                            <th>Status Code</th>
                            <th>Description</th>
                            <th>Common Causes</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><span class="status-code status-200">200</span></td>
                            <td data-lang="zh">请求成功</td>
                            <td data-lang="zh">正常响应</td>
                            <td data-lang="en">Request successful</td>
                            <td data-lang="en">Normal response</td>
                        </tr>
                        <tr>
                            <td><span class="status-code status-400">400</span></td>
                            <td data-lang="zh">请求参数错误</td>
                            <td data-lang="zh">缺少必填参数或参数格式错误</td>
                            <td data-lang="en">Bad request</td>
                            <td data-lang="en">Missing required parameters or invalid format</td>
                        </tr>
                        <tr>
                            <td><span class="status-code status-401">401</span></td>
                            <td data-lang="zh">认证失败</td>
                            <td data-lang="zh">缺少Authorization头或API密钥无效</td>
                            <td data-lang="en">Authentication failed</td>
                            <td data-lang="en">Missing Authorization header or invalid API key</td>
                        </tr>
                        <tr>
                            <td><span class="status-code status-500">500</span></td>
                            <td data-lang="zh">服务器内部错误</td>
                            <td data-lang="zh">上游服务异常或系统错误</td>
                            <td data-lang="en">Internal server error</td>
                            <td data-lang="en">Upstream service exception or system error</td>
                        </tr>
                    </tbody>
                </table>

                <h3>
                    <span data-lang="zh">错误响应格式</span>
                    <span data-lang="en">Error Response Format</span>
                </h3>
                <div class="code-block">
                    <pre>{
  "success": false,
  "error": "Error description / 错误描述信息"
}</pre>
                </div>

                <div class="info-box">
                    <h4>
                        <span data-lang="zh">💡 最佳实践</span>
                        <span data-lang="en">💡 Best Practices</span>
                    </h4>
                    <ul data-lang="zh">
                        <li>始终检查HTTP状态码和响应中的success字段</li>
                        <li>实现适当的重试机制，特别是对于网络错误</li>
                        <li>合理设置轮询间隔（建议2-5秒），避免过于频繁的请求</li>
                        <li>妥善处理超时情况，视频生成通常需要1-5分钟</li>
                    </ul>
                    <ul data-lang="en">
                        <li>Always check HTTP status code and success field in response</li>
                        <li>Implement proper retry mechanisms, especially for network errors</li>
                        <li>Set reasonable polling intervals (2-5 seconds recommended), avoid too frequent requests</li>
                        <li>Handle timeout situations properly, video generation typically takes 1-5 minutes</li>
                    </ul>
                </div>
            </div>
        </section>

        <section class="section">
            <div class="section-header">
                <h2>
                    <span data-lang="zh">🔗 其他接口</span>
                    <span data-lang="en">🔗 Other Endpoints</span>
                </h2>
            </div>
            <div class="section-content">
                <div class="endpoint">
                    <div class="endpoint-header">
                        <div class="endpoint-title">
                            <span class="method get">GET</span>
                            <span class="endpoint-url">/health</span>
                        </div>
                        <div class="endpoint-description">
                            <span data-lang="zh">健康检查接口</span>
                            <span data-lang="en">Health check endpoint</span>
                        </div>
                    </div>
                    <div class="endpoint-content">
                        <p data-lang="zh">用于检查服务是否正常运行。</p>
                        <p data-lang="en">Used to check if the service is running normally.</p>
                        <div class="response-example">
                            <h4>
                                <span data-lang="zh">响应示例</span>
                                <span data-lang="en">Response Example</span>
                                <span class="status-code status-200">200 OK</span>
                            </h4>
                            <div class="code-block">
                                <pre>{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "message": "Video proxy service is running"
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <script>
        let currentLang = 'en';

        function switchLang(lang) {
            currentLang = lang;
            document.body.className = `lang-${lang}`;

            // Update language buttons
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // Update document title
            const title = document.querySelector('title');
            title.textContent = title.getAttribute(`data-${lang}`);

            // Update HTML lang attribute
            document.documentElement.lang = lang === 'zh' ? 'zh-CN' : 'en';
        }

        function copyCode(button) {
            const codeBlock = button.nextElementSibling;
            const text = codeBlock.textContent;

            navigator.clipboard.writeText(text).then(function() {
                const originalText = button.textContent;
                button.textContent = currentLang === 'zh' ? '已复制' : 'Copied';
                button.style.background = '#28a745';

                setTimeout(function() {
                    button.textContent = originalText;
                    button.style.background = '#6c757d';
                }, 2000);
            }).catch(function(err) {
                console.error('Copy failed:', err);
            });
        }

        // Smooth scroll to anchor
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
