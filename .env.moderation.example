# 审核服务配置示例
# Content Moderation Configuration Example

# ===========================================
# 总体审核开关 (Global Moderation Switch)
# ===========================================
# 是否启用审核服务 (Enable moderation service)
# true: 启用审核 | false: 完全关闭审核
MODERATION_ENABLED=true

# ===========================================
# 分类审核开关 (Category-specific Switches)
# ===========================================
# 文本审核开关 (Text moderation switch)
# true: 审核文本内容 | false: 跳过文本审核
TEXT_MODERATION_ENABLED=false

# 图片审核开关 (Image moderation switch)  
# true: 审核图片内容 | false: 跳过图片审核
IMAGE_MODERATION_ENABLED=true

# ===========================================
# 审核严格程度 (Moderation Strictness)
# ===========================================
# 可选值: strict, normal, loose
# strict: 严格模式，对可疑内容零容忍
# normal: 正常模式，平衡安全性和可用性
# loose: 宽松模式，仅拦截明显违规内容
MODERATION_STRICTNESS=normal

# ===========================================
# LLM 审核配置 (LLM Moderation Config)
# ===========================================
# LLM API 地址
MODERATION_LLM_URL=https://api.openai.com/v1/chat/completions

# LLM 模型
MODERATION_LLM_MODEL=gpt-4o-mini

# LLM API 密钥 (必须配置才能启用智能审核)
MODERATION_LLM_API_KEY=your_api_key_here

# LLM 请求超时时间(毫秒)
MODERATION_LLM_TIMEOUT=10000

# LLM 最大重试次数
MODERATION_LLM_MAX_RETRIES=2

# ===========================================
# 图片审核详细配置 (Image Moderation Details)
# ===========================================
# 图片下载超时时间(毫秒)
IMAGE_DOWNLOAD_TIMEOUT=15000

# 最大图片大小(字节) - 默认10MB
IMAGE_MAX_SIZE=10485760

# ===========================================
# 缓存配置 (Cache Configuration)
# ===========================================
# 是否启用审核结果缓存
MODERATION_CACHE_ENABLED=true

# 缓存过期时间(秒) - 默认1小时
MODERATION_CACHE_TTL=3600

# ===========================================
# 常见配置组合示例 (Common Configuration Examples)
# ===========================================

# 1. 只审核图片，不审核文本 (Image only)
# MODERATION_ENABLED=true
# TEXT_MODERATION_ENABLED=false
# IMAGE_MODERATION_ENABLED=true

# 2. 只审核文本，不审核图片 (Text only)
# MODERATION_ENABLED=true
# TEXT_MODERATION_ENABLED=true
# IMAGE_MODERATION_ENABLED=false

# 3. 同时审核文本和图片 (Both text and image)
# MODERATION_ENABLED=true
# TEXT_MODERATION_ENABLED=true
# IMAGE_MODERATION_ENABLED=true

# 4. 完全关闭审核 (Disable all moderation)
# MODERATION_ENABLED=false

# 注意：当 MODERATION_ENABLED=false 时，所有审核都会被跳过，
# 无论 TEXT_MODERATION_ENABLED 和 IMAGE_MODERATION_ENABLED 如何设置
