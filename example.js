// 标准视频API使用示例
const fetch = require('node-fetch');

class StandardVideoClient {
    constructor(baseUrl, apiKey) {
        this.baseUrl = baseUrl;
        this.apiKey = apiKey;
    }

    // 创建视频
    async createVideo(videoParams) {
        const response = await fetch(`${this.baseUrl}/v1/video/create`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.apiKey}`
            },
            body: JSON.stringify(videoParams)
        });

        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.error || `HTTP ${response.status}`);
        }

        return result;
    }

    // 查询视频状态
    async getVideoStatus(videoId) {
        const response = await fetch(`${this.baseUrl}/v1/video/status/${videoId}`);
        const result = await response.json();

        if (!response.ok) {
            throw new Error(result.error || `HTTP ${response.status}`);
        }

        return result;
    }

    // 等待视频完成
    async waitForCompletion(videoId, timeoutMs = 300000) {
        const startTime = Date.now();
        let lastStatus = '';

        while (Date.now() - startTime < timeoutMs) {
            try {
                const result = await this.getVideoStatus(videoId);

                // 状态变化时输出日志
                if (result.status !== lastStatus) {
                    console.log(`Status: ${result.status}`);
                    lastStatus = result.status;
                }

                // 检查是否完成
                if (result.status === 'completed') {
                    return result;
                }

                // 检查是否失败
                if (result.status === 'failed' || result.status.includes('_failed')) {
                    throw new Error(`Video generation failed with status: ${result.status}`);
                }

            } catch (error) {
                console.warn(`Status check error: ${error.message}`);
            }

            // 等待2秒后重试
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        throw new Error('Video generation timeout');
    }
}

// 使用示例
async function main() {
    const client = new StandardVideoClient(
        'http://localhost:8847',  // 中转服务地址
        'sk-your-api-key-here'    // 你的API Key
    );

    try {
        console.log('🎬 Creating video...');

        // 1. 创建视频任务
        const videoParams = {
            prompt: "A serene lake surrounded by autumn trees with falling leaves",
            model: "veo2-fast",
            images: [
                "https://filesystem.site/cdn/20250612/VfgB5ubjInVt8sG6rzMppxnu7gEfde.png"
            ],
            enhance_prompt: true
        };

        const createResult = await client.createVideo(videoParams);

        console.log('✅ Video creation started:');
        console.log(`   ID: ${createResult.id}`);
        console.log(`   Status: ${createResult.status}`);

        if (createResult.enhanced_prompt) {
            console.log(`   Enhanced Prompt: ${createResult.enhanced_prompt.substring(0, 100)}...`);
        }

        // 2. 等待视频完成
        console.log('\n⏳ Waiting for completion...');
        const completedVideo = await client.waitForCompletion(createResult.id);

        console.log('\n🎉 Video completed!');
        console.log(`   Video URL: ${completedVideo.video_url}`);
        console.log(`   Media ID: ${completedVideo.video_media_id}`);

    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

// 批量创建视频示例
async function batchCreateVideos() {
    const client = new StandardVideoClient(
        'http://localhost:8847',
        'sk-your-api-key-here'
    );

    const videoTasks = [
        {
            prompt: "A peaceful mountain landscape at sunrise",
            model: "veo2-fast",
            images: ["https://example.com/mountain.jpg"],
            enhance_prompt: true
        },
        {
            prompt: "Ocean waves crashing on a rocky shore",
            model: "veo3-fast",
            images: ["https://example.com/ocean.jpg"],
            enhance_prompt: true
        },
        {
            prompt: "City skyline with moving clouds",
            model: "veo2-fast-frames",
            images: [
                "https://example.com/city1.jpg",  // 首帧
                "https://example.com/city2.jpg"   // 尾帧
            ],
            enhance_prompt: false
        }
    ];

    console.log(`🎬 Creating ${videoTasks.length} videos...`);

    try {
        // 并发创建所有视频
        const createPromises = videoTasks.map(async (params, index) => {
            try {
                const result = await client.createVideo(params);
                console.log(`✅ Video ${index + 1} created: ${result.id}`);
                return { index: index + 1, id: result.id, params };
            } catch (error) {
                console.error(`❌ Video ${index + 1} failed: ${error.message}`);
                return { index: index + 1, error: error.message, params };
            }
        });

        const createResults = await Promise.all(createPromises);

        // 等待所有视频完成
        const successfulVideos = createResults.filter(r => r.id);
        console.log(`\n⏳ Waiting for ${successfulVideos.length} videos to complete...`);

        const completionPromises = successfulVideos.map(async (video) => {
            try {
                const result = await client.waitForCompletion(video.id);
                console.log(`🎉 Video ${video.index} completed: ${result.video_url}`);
                return { ...video, result };
            } catch (error) {
                console.error(`❌ Video ${video.index} completion failed: ${error.message}`);
                return { ...video, error: error.message };
            }
        });

        const completionResults = await Promise.all(completionPromises);

        // 统计结果
        const successful = completionResults.filter(r => r.result);
        const failed = completionResults.filter(r => r.error);

        console.log(`\n📊 Batch Results:`);
        console.log(`   Successful: ${successful.length}`);
        console.log(`   Failed: ${failed.length}`);

        successful.forEach(video => {
            console.log(`   ✅ Video ${video.index}: ${video.result.video_url}`);
        });

    } catch (error) {
        console.error('❌ Batch creation error:', error.message);
    }
}

// 不同模型的使用示例
async function modelExamples() {
    const client = new StandardVideoClient(
        'http://localhost:8847',
        'sk-your-api-key-here'
    );

    const examples = [
        {
            name: "VEO2 Fast (标准)",
            params: {
                prompt: "A dog running in a park",
                model: "veo2-fast",
                images: ["https://example.com/dog.jpg"],
                enhance_prompt: true
            }
        },
        {
            name: "VEO2 Fast Frames (首尾帧)",
            params: {
                prompt: "Sunset transformation from day to night",
                model: "veo2-fast-frames",
                images: [
                    "https://example.com/day.jpg",    // 首帧
                    "https://example.com/night.jpg"   // 尾帧
                ],
                enhance_prompt: true
            }
        },
        {
            name: "VEO2 Fast Components (多元素组合)",
            params: {
                prompt: "A magical forest scene",
                model: "veo2-fast-components",
                images: [
                    "https://example.com/trees.jpg",     // 背景元素
                    "https://example.com/animals.jpg",   // 动物元素
                    "https://example.com/effects.jpg"    // 特效元素
                ],
                enhance_prompt: true
            }
        },
        {
            name: "VEO3 Fast (带音频)",
            params: {
                prompt: "Musical performance on stage",
                model: "veo3-fast",
                images: ["https://example.com/stage.jpg"],
                enhance_prompt: true
            }
        },
        {
            name: "VEO3 Pro Frames (高质量首帧)",
            params: {
                prompt: "Epic movie scene with dramatic lighting",
                model: "veo3-pro-frames",
                images: ["https://example.com/epic.jpg"],  // 仅首帧
                enhance_prompt: true
            }
        }
    ];

    for (const example of examples) {
        try {
            console.log(`\n🎬 Testing ${example.name}...`);

            const result = await client.createVideo(example.params);
            console.log(`✅ Created: ${result.id} (Status: ${result.status})`);

            // 这里可以选择是否等待完成
            // const completed = await client.waitForCompletion(result.id);
            // console.log(`   Video URL: ${completed.video_url}`);

        } catch (error) {
            console.error(`❌ ${example.name} failed: ${error.message}`);
        }
    }
}

// 根据命令行参数运行不同示例
const mode = process.argv[2] || 'single';

switch (mode) {
    case 'single':
        console.log('Running single video example...');
        main();
        break;
    case 'batch':
        console.log('Running batch video example...');
        batchCreateVideos();
        break;
    case 'models':
        console.log('Running different models example...');
        modelExamples();
        break;
    default:
        console.log('Usage: node video-api-example.js [single|batch|models]');
        console.log('  single - Create a single video (default)');
        console.log('  batch  - Create multiple videos in parallel');
        console.log('  models - Test different model types');
}
