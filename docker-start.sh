#!/bin/bash

# Docker Compose 启动脚本
# 使用方法: ./docker-start.sh [prod|dev|stop|logs|rebuild]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印彩色信息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_docker() {
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
}

# 检查环境文件
check_env() {
    if [ ! -f .env ]; then
        print_warning ".env 文件不存在，从 .env.example 复制..."
        cp .env.example .env
        print_success "已创建 .env 文件，请根据需要修改配置"
    fi
}

# 启动生产环境
start_prod() {
    print_info "启动生产环境..."
    docker-compose up -d video-proxy
    print_success "生产环境已启动"
    print_info "服务地址: http://localhost:${PORT:-8847}"
    print_info "查看日志: ./docker-start.sh logs"
    print_info "停止服务: ./docker-start.sh stop"
}

# 启动开发环境
start_dev() {
    print_info "启动开发环境..."
    docker-compose --profile dev up -d video-proxy-dev
    print_success "开发环境已启动"
    print_info "服务地址: http://localhost:${DEV_PORT:-8848}"
    print_info "查看日志: ./docker-start.sh logs dev"
    print_info "停止服务: ./docker-start.sh stop"
}

# 停止所有服务
stop_services() {
    print_info "停止所有服务..."
    docker-compose --profile dev down
    print_success "所有服务已停止"
}

# 查看日志
show_logs() {
    if [ "$1" = "dev" ]; then
        print_info "查看开发环境日志..."
        docker-compose logs -f video-proxy-dev
    else
        print_info "查看生产环境日志..."
        docker-compose logs -f video-proxy
    fi
}

# 重新构建
rebuild() {
    print_info "重新构建镜像..."
    docker-compose build --no-cache
    print_success "镜像重新构建完成"
}

# 显示帮助信息
show_help() {
    echo "视频代理服务 Docker 启动脚本"
    echo ""
    echo "使用方法:"
    echo "  ./docker-start.sh [命令]"
    echo ""
    echo "命令:"
    echo "  prod     启动生产环境 (默认)"
    echo "  dev      启动开发环境"
    echo "  stop     停止所有服务"
    echo "  logs     查看生产环境日志"
    echo "  logs dev 查看开发环境日志"
    echo "  rebuild  重新构建镜像"
    echo "  help     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  ./docker-start.sh          # 启动生产环境"
    echo "  ./docker-start.sh dev      # 启动开发环境"
    echo "  ./docker-start.sh logs     # 查看日志"
    echo "  ./docker-start.sh stop     # 停止服务"
}

# 主函数
main() {
    check_docker
    check_env

    case "${1:-prod}" in
        "prod")
            start_prod
            ;;
        "dev")
            start_dev
            ;;
        "stop")
            stop_services
            ;;
        "logs")
            show_logs $2
            ;;
        "rebuild")
            rebuild
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@" 