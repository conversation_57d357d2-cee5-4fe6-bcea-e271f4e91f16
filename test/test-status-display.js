/**
 * 测试审核状态显示功能
 */

// 加载环境变量
require('dotenv').config();

const { MODERATION_CONFIG } = require('../config/moderation');

console.log('🧪 测试审核状态显示功能\n');

// 模拟启动时的状态显示
console.log('=== 模拟服务启动时的状态显示 ===');
console.log(`Enabled: ${MODERATION_CONFIG.enabled}`);
console.log(`Strictness: ${MODERATION_CONFIG.strictness}`);
console.log(`Text moderation: ${MODERATION_CONFIG.text.enabled}`);
console.log(`Image moderation: ${MODERATION_CONFIG.image.enabled}`);
console.log(`Cache enabled: ${MODERATION_CONFIG.cache.enabled}`);

// 模拟统计接口的配置信息
console.log('\n=== 模拟统计接口的配置信息 ===');
const statsConfig = {
    enabled: MODERATION_CONFIG.enabled,
    strictness: MODERATION_CONFIG.strictness,
    textModeration: MODERATION_CONFIG.text.enabled,
    imageModeration: MODERATION_CONFIG.image.enabled,
    cacheEnabled: MODERATION_CONFIG.cache.enabled
};

console.log('统计接口配置对象:');
console.log(JSON.stringify(statsConfig, null, 2));

// 测试不同的配置组合
console.log('\n=== 测试不同配置组合的显示效果 ===');

const testConfigs = [
    {
        name: '只审核图片',
        config: {
            enabled: true,
            text: { enabled: false },
            image: { enabled: true },
            cache: { enabled: true },
            strictness: 'normal'
        }
    },
    {
        name: '只审核文本',
        config: {
            enabled: true,
            text: { enabled: true },
            image: { enabled: false },
            cache: { enabled: true },
            strictness: 'normal'
        }
    },
    {
        name: '全部审核',
        config: {
            enabled: true,
            text: { enabled: true },
            image: { enabled: true },
            cache: { enabled: true },
            strictness: 'strict'
        }
    },
    {
        name: '完全关闭',
        config: {
            enabled: false,
            text: { enabled: true },
            image: { enabled: true },
            cache: { enabled: true },
            strictness: 'loose'
        }
    }
];

testConfigs.forEach(test => {
    console.log(`\n📋 ${test.name}:`);
    console.log(`   Enabled: ${test.config.enabled}`);
    console.log(`   Strictness: ${test.config.strictness}`);
    console.log(`   Text moderation: ${test.config.text.enabled}`);
    console.log(`   Image moderation: ${test.config.image.enabled}`);
    console.log(`   Cache enabled: ${test.config.cache.enabled}`);
});

console.log('\n✅ 状态显示测试完成！');
console.log('\n💡 提示：重启服务后，你将在启动日志中看到包含文本审核状态的完整信息。');
