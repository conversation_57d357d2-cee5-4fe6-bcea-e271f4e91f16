/**
 * 审核开关功能测试
 * Test for moderation switches functionality
 */

const moderationService = require('../services/moderationService');
const { MODERATION_CONFIG } = require('../config/moderation');

// 保存原始配置
const originalConfig = { ...MODERATION_CONFIG };

/**
 * 重置配置到原始状态
 */
function resetConfig() {
    Object.assign(MODERATION_CONFIG, originalConfig);
}

/**
 * 测试数据
 */
const testData = {
    textOnly: {
        messages: [{
            content: "Generate a video of a beautiful sunset over the ocean"
        }]
    },
    imageOnly: {
        messages: [{
            content: [{
                type: 'image_url',
                image_url: { url: 'https://example.com/test.jpg' }
            }]
        }]
    },
    textAndImage: {
        messages: [{
            content: [
                { type: 'text', text: 'Create a video based on this image' },
                { type: 'image_url', image_url: { url: 'https://example.com/test.jpg' } }
            ]
        }]
    }
};

/**
 * 测试只审核图片不审核文本
 */
async function testImageOnlyModeration() {
    console.log('\n=== 测试：只审核图片，不审核文本 ===');
    
    // 配置：启用图片审核，禁用文本审核
    MODERATION_CONFIG.enabled = true;
    MODERATION_CONFIG.text.enabled = false;
    MODERATION_CONFIG.image.enabled = true;
    
    console.log('配置状态：');
    console.log(`- 总体审核: ${MODERATION_CONFIG.enabled}`);
    console.log(`- 文本审核: ${MODERATION_CONFIG.text.enabled}`);
    console.log(`- 图片审核: ${MODERATION_CONFIG.image.enabled}`);
    
    // 测试纯文本内容
    console.log('\n1. 测试纯文本内容（应该跳过审核）：');
    const textResult = await moderationService.moderateRequest(testData.textOnly);
    console.log('结果：', textResult);
    
    // 测试纯图片内容
    console.log('\n2. 测试纯图片内容（应该进行审核）：');
    const imageResult = await moderationService.moderateRequest(testData.imageOnly);
    console.log('结果：', imageResult);
    
    // 测试混合内容
    console.log('\n3. 测试文本+图片内容（只审核图片部分）：');
    const mixedResult = await moderationService.moderateRequest(testData.textAndImage);
    console.log('结果：', mixedResult);
    
    resetConfig();
}

/**
 * 测试只审核文本不审核图片
 */
async function testTextOnlyModeration() {
    console.log('\n=== 测试：只审核文本，不审核图片 ===');
    
    // 配置：启用文本审核，禁用图片审核
    MODERATION_CONFIG.enabled = true;
    MODERATION_CONFIG.text.enabled = true;
    MODERATION_CONFIG.image.enabled = false;
    
    console.log('配置状态：');
    console.log(`- 总体审核: ${MODERATION_CONFIG.enabled}`);
    console.log(`- 文本审核: ${MODERATION_CONFIG.text.enabled}`);
    console.log(`- 图片审核: ${MODERATION_CONFIG.image.enabled}`);
    
    // 测试纯文本内容
    console.log('\n1. 测试纯文本内容（应该进行审核）：');
    const textResult = await moderationService.moderateRequest(testData.textOnly);
    console.log('结果：', textResult);
    
    // 测试纯图片内容
    console.log('\n2. 测试纯图片内容（应该跳过审核）：');
    const imageResult = await moderationService.moderateRequest(testData.imageOnly);
    console.log('结果：', imageResult);
    
    // 测试混合内容
    console.log('\n3. 测试文本+图片内容（只审核文本部分）：');
    const mixedResult = await moderationService.moderateRequest(testData.textAndImage);
    console.log('结果：', mixedResult);
    
    resetConfig();
}

/**
 * 测试完全关闭审核
 */
async function testDisableAllModeration() {
    console.log('\n=== 测试：完全关闭审核 ===');
    
    // 配置：关闭总体审核
    MODERATION_CONFIG.enabled = false;
    
    console.log('配置状态：');
    console.log(`- 总体审核: ${MODERATION_CONFIG.enabled}`);
    
    // 测试混合内容
    console.log('\n测试文本+图片内容（应该完全跳过审核）：');
    const result = await moderationService.moderateRequest(testData.textAndImage);
    console.log('结果：', result);
    
    resetConfig();
}

/**
 * 测试同时审核文本和图片
 */
async function testBothModeration() {
    console.log('\n=== 测试：同时审核文本和图片 ===');
    
    // 配置：启用所有审核
    MODERATION_CONFIG.enabled = true;
    MODERATION_CONFIG.text.enabled = true;
    MODERATION_CONFIG.image.enabled = true;
    
    console.log('配置状态：');
    console.log(`- 总体审核: ${MODERATION_CONFIG.enabled}`);
    console.log(`- 文本审核: ${MODERATION_CONFIG.text.enabled}`);
    console.log(`- 图片审核: ${MODERATION_CONFIG.image.enabled}`);
    
    // 测试混合内容
    console.log('\n测试文本+图片内容（应该同时审核文本和图片）：');
    const result = await moderationService.moderateRequest(testData.textAndImage);
    console.log('结果：', result);
    
    resetConfig();
}

/**
 * 运行所有测试
 */
async function runAllTests() {
    console.log('开始测试审核开关功能...\n');
    
    try {
        await testImageOnlyModeration();
        await testTextOnlyModeration();
        await testDisableAllModeration();
        await testBothModeration();
        
        console.log('\n=== 所有测试完成 ===');
        console.log('审核开关功能测试通过！');
        
    } catch (error) {
        console.error('测试过程中出现错误：', error);
    }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
    runAllTests();
}

module.exports = {
    testImageOnlyModeration,
    testTextOnlyModeration,
    testDisableAllModeration,
    testBothModeration,
    runAllTests
};
