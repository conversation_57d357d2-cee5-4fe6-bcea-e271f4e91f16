# 依赖
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# 运行时
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
.nyc_output

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志
logs
*.log

# 编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# 文档
README.md
*.md

# 测试
test/
tests/
__tests__/

# 临时文件
tmp/
temp/ 