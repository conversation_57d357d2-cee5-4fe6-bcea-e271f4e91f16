// 加载环境变量
require('dotenv').config();

const express = require('express');
const fetch = require('node-fetch');
const https = require('https');
const {
    moderationMiddleware,
    moderationStatsMiddleware,
    moderationWhitelistMiddleware,
    moderationRateLimitMiddleware
} = require('./middleware/moderationMiddleware');

// 创建HTTPS Agent，取消证书验证
const httpsAgent = new https.Agent({
    rejectUnauthorized: false, // 取消SSL证书强制验证
    keepAlive: true,
    timeout: 30000
});

const app = express();
app.use(express.json());

// 添加审核统计中间件
app.use(moderationStatsMiddleware());

// 添加审核速率限制中间件
// app.use(moderationRateLimitMiddleware({
//     maxRequests: 50,
//     windowMs: 60000 // 1分钟50次请求
// }));

// 上游服务配置
const UPSTREAM_CONFIG = {
    url: 'http://152.53.170.241:9512/v1/chat/completions'
};

/**
 * 解析流数据，提取Task ID和轮询URL - 更通用的匹配方式
 */
function parseStreamData(chunk) {
    const lines = chunk.toString().split('\n');
    let taskId = null;
    let pollingUrl = null;

    for (const line of lines) {
        if (line.startsWith('data: ')) {
            try {
                const data = JSON.parse(line.slice(6));
                if (data.choices && data.choices[0] && data.choices[0].delta && data.choices[0].delta.content) {
                    const content = data.choices[0].delta.content;

                    // 更通用的URL匹配 - 匹配 https://asyncdata.net/source/ 后面的内容
                    const urlMatch = content.match(/(https:\/\/asyncdata\.net\/source\/[^\s\)]+)/);
                    if (urlMatch) {
                        pollingUrl = urlMatch[1];

                        // 从URL中提取taskId - 取 /source/ 后面的部分
                        const taskIdFromUrl = pollingUrl.match(/\/source\/(.+)$/);
                        if (taskIdFromUrl) {
                            taskId = taskIdFromUrl[1];
                        }
                    }

                    // 如果从URL没有提取到taskId，尝试通用的taskId匹配
                    // 匹配被反引号包围的内容，且包含冒号的格式（如 veo2-fast:uuid）
                    if (!taskId) {
                        const taskIdMatch = content.match(/`([^`]*:[^`]+)`/);
                        if (taskIdMatch) {
                            taskId = taskIdMatch[1];
                        }
                    }
                }
            } catch (e) {
                // 忽略解析错误，继续处理下一行
            }
        }
    }

    return { taskId, pollingUrl };
}

/**
 * 从上游轮询URL获取最新状态
 * 包含重试机制处理网络问题和SSL错误
 */
async function fetchUpstreamStatus(pollingUrl, maxRetries = 3, retryDelay = 1000) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const response = await fetch(pollingUrl, {
                timeout: 30000,
                agent: pollingUrl.startsWith('https:') ? httpsAgent : undefined
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return await response.json();
        } catch (error) {
            lastError = error;

            // 检查是否是网络相关错误，包括SSL错误
            const isNetworkError = error.message.includes('EAI_AGAIN') ||
                                 error.message.includes('ENOTFOUND') ||
                                 error.message.includes('ECONNRESET') ||
                                 error.message.includes('ETIMEDOUT') ||
                                 error.message.includes('EPROTO') ||  // 添加EPROTO错误
                                 error.message.includes('fetch failed');

            if (isNetworkError && attempt < maxRetries) {
                console.warn(`Network error on attempt ${attempt}/${maxRetries} for ${pollingUrl}: ${error.message}. Retrying in ${retryDelay}ms...`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
                // 指数退避：每次重试延迟时间翻倍
                retryDelay *= 2;
                continue;
            }

            // 如果不是网络错误或已达到最大重试次数，直接抛出错误
            break;
        }
    }

    throw new Error(`Failed to fetch upstream status after ${maxRetries} attempts: ${lastError.message}`);
}

/**
 * Chat风格提交接口 - 获取到上游信息后立即返回
 */
app.post('/api/video/submit', moderationMiddleware(), async (req, res) => {
    try {
        const { prompt, model = 'veo2', images = [], ...otherParams } = req.body;

        if (!prompt) {
            return res.status(400).json({
                success: false,
                error: 'prompt is required'
            });
        }

        // 检查Authorization头
        const authorization = req.headers.authorization;
        if (!authorization) {
            return res.status(401).json({
                success: false,
                error: 'Authorization header is required'
            });
        }

        // 构建content，根据是否有图片决定格式
        let content;

        if (images.length > 0) {
            // 有图片时使用数组格式
            content = [
                {
                    type: "text",
                    text: prompt
                }
            ];

            // 添加image_url类型的content
            for (const imageUrl of images) {
                content.push({
                    type: "image_url",
                    image_url: {
                        url: imageUrl
                    }
                });
            }
        } else {
            // 没有图片时直接使用字符串
            content = prompt;
        }

        // 构建上游请求
        const upstreamRequest = {
            model,
            stream: true,
            messages: [
                { role: "user", content: content }
            ],
            ...otherParams
        };

        try {
            console.log(`Starting chat task for prompt: ${prompt}`);
            if (images.length > 0) {
                console.log(`With ${images.length} images: ${images.slice(0, 2).join(', ')}${images.length > 2 ? '...' : ''}`);
            }

            // 发起上游请求，使用客户端传来的Authorization
            const response = await fetch(UPSTREAM_CONFIG.url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authorization
                },
                body: JSON.stringify(upstreamRequest)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            let buffer = '';
            let upstreamTaskId = null;
            let pollingUrl = null;
            let responseResolved = false;

            // 监听流数据
            const onData = chunk => {
                buffer += chunk.toString();

                // 尝试解析已接收的数据
                const { taskId: extractedTaskId, pollingUrl: extractedUrl } = parseStreamData(buffer);

                if (extractedTaskId && extractedUrl && !responseResolved) {
                    upstreamTaskId = extractedTaskId;
                    pollingUrl = extractedUrl;
                    responseResolved = true;

                    console.log(`Got upstream task: ${upstreamTaskId}`);

                    // 立即断开上游连接
                    response.body.removeListener('data', onData);
                    response.body.removeListener('error', onError);
                    response.body.removeListener('end', onEnd);
                    response.body.destroy();

                    // 直接返回上游taskId，不做本地存储
                    res.json({
                        success: true,
                        data: {
                            taskId: upstreamTaskId,  // 直接使用上游taskId
                            pollingUrl,
                            status: 'processing',
                            message: 'Task submitted successfully'
                        }
                    });
                }
            };

            const onError = error => {
                if (!responseResolved) {
                    responseResolved = true;
                    console.error(`Stream error:`, error);
                    res.status(500).json({
                        success: false,
                        error: `Stream error: ${error.message}`
                    });
                }
            };

            const onEnd = () => {
                if (!responseResolved) {
                    responseResolved = true;
                    console.error(`Stream ended without getting task info`);
                    res.status(500).json({
                        success: false,
                        error: 'Failed to extract task information from upstream response'
                    });
                }
            };

            response.body.on('data', onData);
            response.body.on('error', onError);
            response.body.on('end', onEnd);

            // 设置超时，防止无限等待
            setTimeout(() => {
                if (!responseResolved) {
                    responseResolved = true;
                    response.body.destroy();
                    res.status(500).json({
                        success: false,
                        error: 'Timeout waiting for upstream task information'
                    });
                }
            }, 30000); // 30秒超时

        } catch (error) {
            console.error(`Request error:`, error);
            res.status(500).json({
                success: false,
                error: `Request error: ${error.message}`
            });
        }

    } catch (error) {
        console.error('Submit error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
});

/**
 * Chat风格查询接口 - 直接请求上游
 */
app.get('/api/video/status/:taskId', async (req, res) => {
    try {
        const { taskId } = req.params;

        // 构建上游轮询URL
        const pollingUrl = `https://asyncdata.net/source/${taskId}`;

        try {
            console.log(`Querying upstream status for: ${taskId}`);
            const upstreamStatus = await fetchUpstreamStatus(pollingUrl);

            // 构建响应
            const response = {
                success: true,
                data: {
                    taskId,
                    pollingUrl,
                    upstreamData: upstreamStatus
                }
            };

            // 根据上游状态判断任务状态
            if (upstreamStatus.video_url) {
                response.data.status = 'completed';
                response.data.result = {
                    video_url: upstreamStatus.video_url,
                    video_media_id: upstreamStatus.video_media_id
                };
            } else if (upstreamStatus.status === 'failed' || upstreamStatus.retry_count > upstreamStatus.max_retries) {
                response.data.status = 'failed';
                response.data.error = 'Video generation failed';
            } else {
                response.data.status = 'processing';
                response.data.progress = {
                    upstreamStatus: upstreamStatus.status,
                    videoGenerationStatus: upstreamStatus.video_generation_status,
                    upsampling: upstreamStatus.upsample_status || null,
                    retryCount: upstreamStatus.retry_count,
                    maxRetries: upstreamStatus.max_retries
                };
            }

            res.json(response);

        } catch (error) {
            console.error(`Failed to fetch status for ${taskId}:`, error);
            res.status(500).json({
                success: false,
                error: `Failed to fetch task status: ${error.message}`
            });
        }

    } catch (error) {
        console.error('Status query error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
});

/**
 * 标准视频创建接口 - 接收视频参数并转换为chat格式
 */
app.post('/v1/video/create', moderationMiddleware(), async (req, res) => {
    try {
        const { prompt, model, images = [], enhance_prompt = true } = req.body;

        // 参数验证
        if (!prompt) {
            return res.status(400).json({
                success: false,
                error: 'prompt is required'
            });
        }

        if (!model) {
            return res.status(400).json({
                success: false,
                error: 'model is required'
            });
        }

        // 验证images参数（可选）
        if (!Array.isArray(images)) {
            return res.status(400).json({
                success: false,
                error: 'images must be an array'
            });
        }

        if (images.length > 3) {
            return res.status(400).json({
                success: false,
                error: 'images array cannot contain more than 3 images'
            });
        }

        // 检查Authorization头
        const authorization = req.headers.authorization;
        if (!authorization) {
            return res.status(401).json({
                success: false,
                error: 'Authorization header is required'
            });
        }

        // 构建content，根据是否有图片决定格式
        let content;

        if (images.length > 0) {
            // 有图片时使用数组格式
            content = [
                {
                    type: "text",
                    text: prompt
                }
            ];

            // 添加image_url类型的content
            for (const imageUrl of images) {
                content.push({
                    type: "image_url",
                    image_url: {
                        url: imageUrl
                    }
                });
            }
        } else {
            // 没有图片时直接使用字符串
            content = prompt;
        }

        // 构建上游请求（chat格式）
        const upstreamRequest = {
            model,
            stream: true,
            messages: [
                { role: "user", content: content }
            ],
            // 添加原始参数作为额外参数
            enhance_prompt
        };

        try {
            console.log(`Starting video creation`);
            console.log(`Prompt: ${prompt}`);
            console.log(`Model: ${model}`);
            console.log(`Images: ${images.length} items`);
            if (images.length > 0) {
                console.log(`Image URLs: ${images.slice(0, 2).join(', ')}${images.length > 2 ? '...' : ''}`);
            }

            // 发起上游请求，使用客户端传来的Authorization
            const response = await fetch(UPSTREAM_CONFIG.url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authorization
                },
                body: JSON.stringify(upstreamRequest)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            let buffer = '';
            let upstreamTaskId = null;
            let pollingUrl = null;
            let enhancedPrompt = null;
            let responseResolved = false;

            // 监听流数据
            const onData = chunk => {
                buffer += chunk.toString();

                // 尝试解析已接收的数据
                const { taskId: extractedTaskId, pollingUrl: extractedUrl } = parseStreamData(buffer);

                // 尝试提取enhanced_prompt
                if (!enhancedPrompt) {
                    const enhancedMatch = buffer.match(/enhanced[_\s]prompt[:\s]*["']([^"']+)["']/i);
                    if (enhancedMatch) {
                        enhancedPrompt = enhancedMatch[1];
                    }
                }

                if (extractedTaskId && extractedUrl && !responseResolved) {
                    upstreamTaskId = extractedTaskId;
                    pollingUrl = extractedUrl;
                    responseResolved = true;

                    console.log(`Video task created: ${upstreamTaskId}`);

                    // 立即断开上游连接
                    response.body.removeListener('data', onData);
                    response.body.removeListener('error', onError);
                    response.body.removeListener('end', onEnd);
                    response.body.destroy();

                    // 返回符合API规范的结果
                    const responseData = {
                        id: upstreamTaskId,  // 直接使用上游taskId
                        status: 'pending',
                        status_update_time: Date.now()
                    };

                    // 如果有enhanced_prompt则包含
                    if (enhancedPrompt) {
                        responseData.enhanced_prompt = enhancedPrompt;
                    }

                    res.json(responseData);
                }
            };

            const onError = error => {
                if (!responseResolved) {
                    responseResolved = true;
                    console.error(`Stream error for video task:`, error);
                    res.status(500).json({
                        success: false,
                        error: `Stream error: ${error.message}`
                    });
                }
            };

            const onEnd = () => {
                if (!responseResolved) {
                    responseResolved = true;
                    console.error(`Stream ended without getting task info for video task`);
                    res.status(500).json({
                        success: false,
                        error: 'Failed to extract task information from upstream response'
                    });
                }
            };

            response.body.on('data', onData);
            response.body.on('error', onError);
            response.body.on('end', onEnd);

            // 设置超时，防止无限等待
            setTimeout(() => {
                if (!responseResolved) {
                    responseResolved = true;
                    response.body.destroy();
                    res.status(500).json({
                        success: false,
                        error: 'Timeout waiting for upstream task information'
                    });
                }
            }, 30000); // 30秒超时

        } catch (error) {
            console.error(`Request error for video task:`, error);
            res.status(500).json({
                success: false,
                error: `Request error: ${error.message}`
            });
        }

    } catch (error) {
        console.error('Video creation error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
});

/**
 * 查询视频任务状态 - 符合API规范的响应格式
 */
app.get('/v1/video/status/:taskId', async (req, res) => {
    try {
        const { taskId } = req.params;

        // 构建上游轮询URL
        const pollingUrl = `https://asyncdata.net/source/${taskId}`;

        try {
            console.log(`Querying video status for: ${taskId}`);
            const upstreamStatus = await fetchUpstreamStatus(pollingUrl);

            // 将上游状态映射到API规范的状态
            let apiStatus = 'pending';
            if (upstreamStatus.video_url) {
                apiStatus = 'completed';
            } else if (upstreamStatus.status === 'failed' || upstreamStatus.retry_count >= upstreamStatus.max_retries) {
                apiStatus = 'failed';
            } else {
                // 根据上游状态映射
                switch (upstreamStatus.status) {
                    case 'video_generating':
                        apiStatus = 'video_generating';
                        break;
                    case 'video_upsampling':
                        apiStatus = 'video_upsampling';
                        break;
                    default:
                        apiStatus = 'pending';
                }
            }

            // 构建符合API规范的响应
            const response = {
                id: taskId,
                status: apiStatus,
                status_update_time: upstreamStatus.status_update_time || Date.now()
            };

            // 添加可选字段
            if (upstreamStatus.video_url) {
                response.video_url = upstreamStatus.video_url;
            }

            if (upstreamStatus.video_media_id) {
                response.video_media_id = upstreamStatus.video_media_id;
            }

            res.json(response);

        } catch (error) {
            console.error(`Failed to fetch video status for ${taskId}:`, error);
            res.status(404).json({
                success: false,
                error: `Task not found or upstream error: ${error.message}`
            });
        }

    } catch (error) {
        console.error('Video status query error:', error);
        res.status(500).json({
            success: false,
            error: 'Internal server error'
        });
    }
});

/**
 * 审核统计接口
 */
app.get('/api/moderation/stats', (req, res) => {
    const moderationService = require('./services/moderationService');
    const stats = moderationService.getStats();

    res.json({
        success: true,
        data: {
            ...stats,
            config: {
                enabled: require('./config/moderation').MODERATION_CONFIG.enabled,
                strictness: require('./config/moderation').MODERATION_CONFIG.strictness,
                textModeration: require('./config/moderation').MODERATION_CONFIG.text.enabled,
                imageModeration: require('./config/moderation').MODERATION_CONFIG.image.enabled,
                cacheEnabled: require('./config/moderation').MODERATION_CONFIG.cache.enabled
            }
        },
        timestamp: new Date().toISOString()
    });
});

/**
 * 内容审核测试接口
 */
app.post('/api/moderation/test', async (req, res) => {
    try {
        const { text, imageUrl } = req.body;
        const moderationService = require('./services/moderationService');

        const results = [];

        // 审核文字内容
        if (text) {
            const textResult = await moderationService.moderateText(text);
            results.push({
                type: 'text',
                content: text,
                ...textResult
            });
        }

        // 审核图片内容
        if (imageUrl) {
            const imageResult = await moderationService.moderateImage(imageUrl);
            results.push({
                type: 'image',
                content: imageUrl,
                ...imageResult
            });
        }

        // 汇总审核结果
        const finalResult = moderationService.aggregateResults(results);

        res.json({
            success: true,
            data: finalResult,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Moderation test error:', error);
        res.status(500).json({
            success: false,
            error: 'Moderation test failed',
            message: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

/**
 * 清理审核缓存接口
 */
app.post('/api/moderation/clear-cache', (req, res) => {
    const moderationService = require('./services/moderationService');
    moderationService.clearCache();

    res.json({
        success: true,
        message: 'Moderation cache cleared',
        timestamp: new Date().toISOString()
    });
});

/**
 * 获取审核规则接口
 */
app.get('/api/moderation/rules', (req, res) => {
    try {
        const moderationRulesService = require('./services/moderationRulesService');
        const rules = moderationRulesService.getAllRules();

        res.json({
            success: true,
            data: rules,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Get rules error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to get moderation rules',
            message: error.message
        });
    }
});

/**
 * 添加敏感词接口
 */
app.post('/api/moderation/sensitive-words', async (req, res) => {
    try {
        const { category, words } = req.body;

        if (!category || !words || !Array.isArray(words)) {
            return res.status(400).json({
                success: false,
                error: 'Category and words array are required'
            });
        }

        const moderationRulesService = require('./services/moderationRulesService');
        const result = await moderationRulesService.addSensitiveWords(category, words);

        res.json({
            success: true,
            data: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Add sensitive words error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to add sensitive words',
            message: error.message
        });
    }
});

/**
 * 删除敏感词接口
 */
app.delete('/api/moderation/sensitive-words', async (req, res) => {
    try {
        const { category, words } = req.body;

        if (!category || !words || !Array.isArray(words)) {
            return res.status(400).json({
                success: false,
                error: 'Category and words array are required'
            });
        }

        const moderationRulesService = require('./services/moderationRulesService');
        const result = await moderationRulesService.removeSensitiveWords(category, words);

        res.json({
            success: true,
            data: result,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        console.error('Remove sensitive words error:', error);
        res.status(500).json({
            success: false,
            error: 'Failed to remove sensitive words',
            message: error.message
        });
    }
});

/**
 * 健康检查接口
 */
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        message: 'Video proxy service is running',
        uptime: process.uptime()
    });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('Unhandled error:', error);
    res.status(500).json({
        success: false,
        error: 'Internal server error'
    });
});

const PORT = process.env.PORT || 8847;
app.listen(PORT, () => {
    console.log(`Video generation proxy server running on port ${PORT}`);
    console.log(`Health check: http://localhost:${PORT}/health`);
    console.log(`\n=== Chat-style Interface ===`);
    console.log(`Submit task: POST http://localhost:${PORT}/api/video/submit`);
    console.log(`Query status: GET http://localhost:${PORT}/api/video/status/{taskId}`);
    console.log(`\n=== Standard Video API Interface ===`);
    console.log(`Create video: POST http://localhost:${PORT}/v1/video/create`);
    console.log(`Query video: GET http://localhost:${PORT}/v1/video/status/{taskId}`);
    console.log(`\n=== Content Moderation Interface ===`);
    console.log(`Test content: POST http://localhost:${PORT}/api/moderation/test`);
    console.log(`View stats: GET http://localhost:${PORT}/api/moderation/stats`);
    console.log(`Manage rules: GET http://localhost:${PORT}/api/moderation/rules`);
    console.log(`Clear cache: POST http://localhost:${PORT}/api/moderation/clear-cache`);

    // 显示审核配置状态
    const { MODERATION_CONFIG } = require('./config/moderation');
    console.log(`\n=== Moderation Status ===`);
    console.log(`Enabled: ${MODERATION_CONFIG.enabled}`);
    console.log(`Strictness: ${MODERATION_CONFIG.strictness}`);
    console.log(`Text moderation: ${MODERATION_CONFIG.text.enabled}`);
    console.log(`Image moderation: ${MODERATION_CONFIG.image.enabled}`);
    console.log(`Cache enabled: ${MODERATION_CONFIG.cache.enabled}`);
});

module.exports = app;
