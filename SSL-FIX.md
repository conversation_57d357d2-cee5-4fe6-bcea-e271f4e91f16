# SSL错误修复说明

## 问题
你遇到的错误：
```
Error: Failed to fetch upstream status after 3 attempts: request to https://asyncdata.net/source/veo3:xxx failed, reason: write EPROTO 986B126D7F7F0000:error:0A000438:SSL routines:ssl3_read_bytes:tlsv1 alert internal error
```

## 解决方案

### 1. 取消SSL证书强制验证
```javascript
// 创建HTTPS Agent，取消证书验证
const httpsAgent = new https.Agent({
    rejectUnauthorized: false, // 取消SSL证书强制验证
    keepAlive: true,
    timeout: 30000
});
```

### 2. 在重试错误类别中添加EPROTO
```javascript
// 检查是否是网络相关错误，包括SSL错误
const isNetworkError = error.message.includes('EAI_AGAIN') ||
                     error.message.includes('ENOTFOUND') ||
                     error.message.includes('ECONNRESET') ||
                     error.message.includes('ETIMEDOUT') ||
                     error.message.includes('EPROTO') ||  // 添加EPROTO错误
                     error.message.includes('fetch failed');
```

## 修改内容

1. **app.js 第14-19行**: 创建简化的HTTPS Agent
2. **app.js 第100行**: 在重试错误检查中添加EPROTO

## 效果

- ✅ 跳过SSL证书验证，避免证书相关错误
- ✅ EPROTO错误会被识别为可重试错误
- ✅ 保持连接复用提高性能
- ✅ 代码简洁，易于维护

这个简单的修改应该能解决你遇到的SSL连接问题。
