/**
 * Google Veo内容审核系统使用示例
 */

// 加载环境变量
require('dotenv').config();

const fetch = require('node-fetch');

// 服务器配置
const SERVER_URL = 'http://localhost:8847';
const API_KEY = 'your-api-key-here';

/**
 * 示例1: 测试文字内容审核
 */
async function testTextModeration() {
    console.log('\n=== 文字内容审核测试 ===');
    
    const testCases = [
        {
            name: '正常内容',
            text: '一个美丽的日落场景，金色的阳光洒在宁静的湖面上，远山如黛，鸟儿在天空中自由飞翔。'
        },
        {
            name: '暴力内容',
            text: '一个血腥的战斗场面，士兵们互相厮杀，到处都是鲜血和尸体。'
        },
        {
            name: '不当内容',
            text: '一个性感的女性在脱衣服，展示她的身体。'
        },
        {
            name: '儿童安全问题',
            text: '一个小孩独自在危险的地方玩耍，没有大人监护。'
        }
    ];
    
    for (const testCase of testCases) {
        try {
            console.log(`\n测试: ${testCase.name}`);
            console.log(`内容: ${testCase.text}`);
            
            const response = await fetch(`${SERVER_URL}/api/moderation/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    text: testCase.text
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log(`审核结果: ${result.data.approved ? '✅ 通过' : '❌ 不通过'}`);
                if (!result.data.approved) {
                    console.log(`违规类型: ${result.data.violations?.join(', ') || '未知'}`);
                    console.log(`改进建议: ${result.data.suggestions || '无'}`);
                    console.log(`Google Veo政策合规: ${result.data.veo_policy_compliance || '未知'}`);
                }
            } else {
                console.log(`审核失败: ${result.error}`);
            }
        } catch (error) {
            console.error(`测试失败: ${error.message}`);
        }
    }
}

/**
 * 示例2: 测试图片内容审核
 */
async function testImageModeration() {
    console.log('\n=== 图片内容审核测试 ===');
    
    const testImages = [
        {
            name: '风景图片',
            url: 'https://example.com/landscape.jpg'
        },
        {
            name: '人物图片',
            url: 'https://example.com/person.jpg'
        }
    ];
    
    for (const testImage of testImages) {
        try {
            console.log(`\n测试: ${testImage.name}`);
            console.log(`图片URL: ${testImage.url}`);
            
            const response = await fetch(`${SERVER_URL}/api/moderation/test`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    imageUrl: testImage.url
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log(`审核结果: ${result.data.approved ? '✅ 通过' : '❌ 不通过'}`);
                if (!result.data.approved) {
                    console.log(`违规类型: ${result.data.violations?.join(', ') || '未知'}`);
                    console.log(`改进建议: ${result.data.suggestions || '无'}`);
                    console.log(`Google Veo政策合规: ${result.data.veo_policy_compliance || '未知'}`);
                }
            } else {
                console.log(`审核失败: ${result.error}`);
            }
        } catch (error) {
            console.error(`测试失败: ${error.message}`);
        }
    }
}

/**
 * 示例3: 完整的视频生成请求（包含审核）
 */
async function testVideoGenerationWithModeration() {
    console.log('\n=== 视频生成请求测试（包含审核） ===');
    
    const videoRequest = {
        messages: [
            {
                role: "user",
                content: [
                    {
                        type: "text",
                        text: "一个第一人称自拍视频，镜头平稳移动，角色走过战斗后的废墟。角色表情坚毅，带着一丝微笑。"
                    },
                    {
                        type: "image_url",
                        image_url: {
                            url: "https://example.com/reference-image.jpg"
                        }
                    }
                ]
            }
        ],
        model: "veo3-pro-frames",
        stream: true
    };
    
    try {
        console.log('发送视频生成请求...');
        
        const response = await fetch(`${SERVER_URL}/api/video/submit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${API_KEY}`
            },
            body: JSON.stringify(videoRequest)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            console.log('✅ 请求成功提交');
            console.log(`任务ID: ${result.data.taskId}`);
            console.log(`轮询URL: ${result.data.pollingUrl}`);
        } else {
            console.log('❌ 请求被拒绝');
            if (result.code === 'CONTENT_MODERATION_FAILED') {
                console.log('拒绝原因: 内容审核失败');
                console.log(`违规类型: ${result.details.violations?.join(', ') || '未知'}`);
                console.log(`改进建议: ${result.details.suggestions || '无'}`);
            } else {
                console.log(`错误: ${result.error}`);
            }
        }
    } catch (error) {
        console.error(`请求失败: ${error.message}`);
    }
}

/**
 * 示例4: 查看审核统计信息
 */
async function viewModerationStats() {
    console.log('\n=== 审核统计信息 ===');
    
    try {
        const response = await fetch(`${SERVER_URL}/api/moderation/stats`);
        const result = await response.json();
        
        if (result.success) {
            const stats = result.data;
            console.log(`总请求数: ${stats.totalRequests}`);
            console.log(`通过请求数: ${stats.approvedRequests}`);
            console.log(`拒绝请求数: ${stats.rejectedRequests}`);
            console.log(`通过率: ${stats.approvalRate}`);
            console.log(`缓存命中数: ${stats.cacheHits}`);
            console.log(`缓存大小: ${stats.cacheSize}`);
            console.log(`审核状态: ${stats.config.enabled ? '启用' : '禁用'}`);
            console.log(`严格程度: ${stats.config.strictness}`);
        } else {
            console.log(`获取统计失败: ${result.error}`);
        }
    } catch (error) {
        console.error(`获取统计失败: ${error.message}`);
    }
}

/**
 * 示例5: 管理自定义敏感词
 */
async function manageSensitiveWords() {
    console.log('\n=== 敏感词管理 ===');
    
    try {
        // 添加自定义敏感词
        console.log('添加自定义敏感词...');
        const addResponse = await fetch(`${SERVER_URL}/api/moderation/sensitive-words`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                category: 'custom_veo',
                words: ['不当内容示例1', '不当内容示例2']
            })
        });
        
        const addResult = await addResponse.json();
        if (addResult.success) {
            console.log(`✅ 成功添加 ${addResult.data.added} 个敏感词`);
            console.log(`当前总数: ${addResult.data.total}`);
        }
        
        // 查看当前规则
        console.log('\n查看当前审核规则...');
        const rulesResponse = await fetch(`${SERVER_URL}/api/moderation/rules`);
        const rulesResult = await rulesResponse.json();
        
        if (rulesResult.success) {
            console.log('当前规则配置:');
            console.log(JSON.stringify(rulesResult.data, null, 2));
        }
        
    } catch (error) {
        console.error(`管理敏感词失败: ${error.message}`);
    }
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
    console.log('🚀 Google Veo内容审核系统示例');
    console.log('=====================================');
    
    await testTextModeration();
    await testImageModeration();
    await testVideoGenerationWithModeration();
    await viewModerationStats();
    await manageSensitiveWords();
    
    console.log('\n✅ 所有示例运行完成');
}

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
    runAllExamples().catch(console.error);
}

module.exports = {
    testTextModeration,
    testImageModeration,
    testVideoGenerationWithModeration,
    viewModerationStats,
    manageSensitiveWords,
    runAllExamples
};
