/**
 * 内容审核配置
 */

// 审核配置
const MODERATION_CONFIG = {
    // 是否启用审核
    enabled: process.env.MODERATION_ENABLED !== 'false',
    
    // 审核严格程度 (strict, normal, loose)
    strictness: process.env.MODERATION_STRICTNESS || 'normal',
    
    // 大模型审核配置
    llm: {
        // 审核模型API地址
        url: process.env.MODERATION_LLM_URL || 'https://api.openai.com/v1/chat/completions',
        // 审核模型
        model: process.env.MODERATION_LLM_MODEL || 'gpt-4o-mini',
        // API密钥
        apiKey: process.env.MODERATION_LLM_API_KEY || '',
        // 请求超时时间(毫秒)
        timeout: parseInt(process.env.MODERATION_LLM_TIMEOUT) || 10000,
        // 最大重试次数
        maxRetries: parseInt(process.env.MODERATION_LLM_MAX_RETRIES) || 2
    },
    
    // 文本审核配置
    text: {
        // 是否启用文本审核
        enabled: process.env.TEXT_MODERATION_ENABLED !== 'false',
    },

    // 图片审核配置
    image: {
        // 是否启用图片审核
        enabled: process.env.IMAGE_MODERATION_ENABLED !== 'false',
        // 图片下载超时时间(毫秒)
        downloadTimeout: parseInt(process.env.IMAGE_DOWNLOAD_TIMEOUT) || 15000,
        // 支持的图片格式
        supportedFormats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        // 最大图片大小(字节)
        maxSize: parseInt(process.env.IMAGE_MAX_SIZE) || 10 * 1024 * 1024, // 10MB
    },
    
    // 缓存配置
    cache: {
        // 是否启用缓存
        enabled: process.env.MODERATION_CACHE_ENABLED !== 'false',
        // 缓存过期时间(秒)
        ttl: parseInt(process.env.MODERATION_CACHE_TTL) || 3600, // 1小时
    }
};

// 敏感词库 - 基于Google Veo内容政策
const SENSITIVE_WORDS = {
    // 暴力和危险内容
    violence: [
        '杀死', '杀害', '谋杀', '暴力', '血腥', '残忍', '虐待', '折磨',
        '枪击', '爆炸', '恐怖主义', '自杀', '自残', '伤害他人', '斩首',
        '酷刑', '处决', '屠杀', '战争场面', '武器制造', '炸弹制作',
        '暴力威胁', '死亡威胁', '极端暴力', '血腥场面', '尸体',
        '危险挑战', '极限运动', '自我伤害', '他人伤害'
    ],

    // 成人和性内容
    sexual: [
        '色情', '裸体', '性行为', '成人内容', '不雅', '淫秽',
        '性暗示', '挑逗', '诱惑', '露骨', '性器官', '性交',
        '成人娱乐', '脱衣', '性感', '情色', '春宫', '艳情',
        '性服务', '卖淫', '性交易', '性虐待', '性骚扰'
    ],

    // 仇恨言论和歧视
    hate: [
        '种族歧视', '性别歧视', '宗教歧视', '仇恨言论',
        '歧视性语言', '侮辱性词汇', '人身攻击', '种族优越',
        '民族仇恨', '宗教仇恨', '性取向歧视', '残疾歧视',
        '年龄歧视', '外貌歧视', '霸凌', '网络暴力', '煽动仇恨',
        '极端主义', '白人至上', '纳粹', '法西斯'
    ],

    // 非法活动
    illegal: [
        '毒品', '走私', '洗钱', '诈骗', '盗窃', '抢劫',
        '非法交易', '黑市', '违法犯罪', '非法武器', '人口贩卖',
        '器官买卖', '儿童拐卖', '非法赌博', '洗黑钱', '逃税',
        '贿赂', '腐败', '非法集资', '传销', '诈骗集团',
        '网络犯罪', '黑客攻击', '身份盗用', '信用卡诈骗'
    ],

    // 儿童安全
    child_safety: [
        '儿童色情', '未成年人', '儿童剥削', '儿童虐待',
        '儿童性侵', '恋童癖', '儿童裸体', '未成年性行为',
        '儿童诱拐', '儿童拐卖', '校园霸凌', '儿童自残',
        '儿童危险', '未成年饮酒', '未成年吸烟', '儿童暴力'
    ],

    // 误导信息
    misinformation: [
        '虚假医疗', '伪科学', '阴谋论', '假新闻', '虚假信息',
        '医疗骗局', '治疗骗局', '疫苗阴谋', '选举舞弊',
        '政府阴谋', '外星人', 'UFO阴谋', '地球平坦论',
        '反疫苗', '医疗误导', '健康误导', '科学否认'
    ],

    // 版权和知识产权
    copyright: [
        '盗版', '版权侵犯', '商标侵权', '抄袭', '剽窃',
        '未经授权', '非法复制', '盗用', '侵权', '假冒',
        '山寨', '仿制', '非法下载', '破解软件', '盗版软件'
    ],

    // 隐私侵犯
    privacy: [
        '个人信息泄露', '隐私侵犯', '偷拍', '偷录', '跟踪',
        '监视', '窃听', '身份信息', '银行信息', '密码泄露',
        '个人隐私', '私人照片', '未经同意', '恶意跟踪',
        '网络跟踪', '信息窃取', '数据泄露'
    ],

    // 政治敏感（根据地区调整）
    political: [
        '政治敏感', '反政府', '颠覆', '分裂', '独立运动',
        '政治抗议', '革命', '政变', '政治暴力', '选举干预',
        '政治煽动', '政权颠覆', '国家分裂', '民族分裂',
        '宗教极端', '政治极端', '暴力革命'
    ],

    // 名人肖像权
    celebrity: [
        '明星', '名人', '演员', '歌手', '艺人', '偶像',
        '网红', '主播', '博主', '公众人物', '知名人士',
        '体育明星', '运动员', '政治家', '领导人', '总统',
        '首相', '部长', '市长', '名人照片', '明星照片',
        '肖像权', '人物肖像', '真人照片', '名人形象',
        '公众人物照片', '未经授权使用', '名人代言'
    ]
};

// 审核提示词模板 - 多语言支持
const MODERATION_PROMPTS = {
    // 中文审核提示词
    zh: {
        textModeration: `你是一个专业的内容审核员，专门负责审核用于Google Veo视频生成的内容。请严格按照Google Veo的内容政策和使用条款审核以下文字内容：

内容: "{content}"

请严格按照Google Veo视频生成政策进行审核，重点检查以下违规内容：

**Google Veo禁止的内容类型：**
1. **暴力和危险内容**：
   - 极端暴力、血腥场面、感官刺激的暴力内容
   - 自残、自杀相关内容，包括饮食失调指导
   - 危险活动或挑战的详细指导
   - 武器制造或使用指导
   - 对动物的无端暴力

2. **成人和性内容**：
   - 性暗示或性行为描述
   - 裸体或半裸体内容
   - 成人娱乐相关内容
   - 色情或情色内容
   - 性暴力或性侵犯的描述

3. **仇恨言论和歧视**：
   - 基于种族、性别、宗教、性取向等的歧视
   - 仇恨言论或煽动仇恨
   - 骚扰、霸凌、威胁个人或群体
   - 非人化言论或将受保护群体比作动物
   - 暴力攻击的煽动

4. **非法活动和危险指导**：
   - 毒品制造、购买或销售指导
   - 非法武器交易
   - 其他违法犯罪活动的详细指导
   - 购买非法物品的指导

5. **误导信息和虚假内容**：
   - 医疗误导信息，与科学共识相冲突的健康建议
   - 选举相关虚假信息和欺诈指控
   - 可能引起恐慌的虚假灾难信息
   - 阴谋论或极端观点
   - 深度伪造内容和虚假新闻事件
   - 冒充他人（包括已故人员）进行欺骗

6. **儿童安全和未成年人保护**：
   - **严格禁止任何涉及未成年人的内容**
   - 涉及未成年人的不当内容
   - 儿童剥削相关内容
   - 为保护未成年人，一律拒绝包含儿童或青少年的描述

7. **知识产权侵权**：
   - 未经授权使用受版权保护的内容
   - 商标侵权内容

8. **名人肖像权和身份冒充**：
   - **禁止使用知名名人、公众人物的照片或肖像**
   - 包括但不限于著名演员、歌手、政治家、体育明星、知名网红等
   - 重点关注广泛知名的公众人物，而非普通个人
   - 只有在能够清楚识别为特定知名名人或公众人物时才拒绝
   - 禁止创建可能被误认为真实人物的深度伪造内容
   - 要求生成特定真实人物的形象或外貌
   - 模仿或冒充知名人士的内容

9. **隐私侵犯和监控**：
   - 未经同意分享他人私人信息
   - 恶意跟踪或监视内容
   - 包含个人身份信息的描述
   - 未经同意的私人信息披露

10. **网络安全威胁**：
    - 垃圾邮件、钓鱼或恶意软件相关内容
    - 滥用、损害或干扰服务基础设施
    - 绕过安全保护或安全过滤器的尝试

请以JSON格式返回审核结果：
{
  "approved": true/false,
  "confidence": 0.0-1.0,
  "violations": ["具体违规类型"],
  "suggestions": "符合Google Veo政策的改进建议",
  "reason": "基于Google Veo政策的审核理由",
  "veo_policy_compliance": "是否符合Google Veo内容政策"
}

审核严格程度: {strictness}
注意：必须严格遵守Google Veo的内容政策，确保生成的视频内容安全、合法且符合平台规范。**特别强调：严禁任何涉及名人、明星、公众人物肖像的内容请求。**请用中文回复。`,

        imageModeration: `你是一个专业的图片内容审核员，专门负责审核用于Google Veo视频生成的图片内容。请严格按照Google Veo的内容政策和使用条款审核这张图片：

请严格按照Google Veo视频生成政策进行审核，重点检查以下违规内容：

**Google Veo禁止的图片内容：**
1. **暴力和危险场景**：
   - 血腥、暴力场面、感官刺激的暴力内容
   - 武器展示或威胁性姿态
   - 危险活动或极限运动的详细展示
   - 事故或灾难场景
   - 对动物的无端暴力场景
   - 自残或自杀相关图像

2. **成人和性内容**：
   - 裸体或半裸体
   - 性暗示姿态或场景
   - 成人娱乐相关图像
   - 色情或情色内容
   - 性暴力或性侵犯场景

3. **仇恨符号和歧视内容**：
   - 仇恨符号或标志
   - 歧视性手势或标语
   - 极端主义相关图像
   - 种族主义、性别歧视等歧视性内容
   - 煽动暴力或仇恨的图像

4. **非法活动场景**：
   - 毒品相关物品或制造场景
   - 非法武器
   - 犯罪活动场景
   - 非法交易或走私场景

5. **儿童安全和未成年人保护**：
   - **严格禁止任何包含未成年人的图片内容**
   - 涉及未成年人的不当场景
   - 儿童处于危险环境
   - 为保护未成年人隐私和安全，一律拒绝包含儿童或青少年的图像

6. **隐私侵犯和监控**：
   - 未经同意的私人照片
   - 包含个人身份信息的图像
   - 恶意跟踪或监视相关图像
   - 未经授权的生物识别数据

7. **版权侵权**：
   - 受版权保护的艺术作品
   - 商标或品牌标识
   - 电影、电视剧截图
   - 未经授权的受保护内容

8. **名人肖像权和身份冒充**：
   - **禁止使用知名名人、公众人物的照片或肖像**
   - 包括但不限于著名演员、歌手、政治家、体育明星、知名网红等
   - 重点关注广泛知名的公众人物，而非普通个人
   - 只有在能够清楚识别为特定知名名人或公众人物时才拒绝
   - 可能被误认为真实人物的深度伪造图像

9. **误导性内容和虚假信息**：
   - 深度伪造或AI生成的虚假图像
   - 误导性医疗图像
   - 虚假新闻事件的图像
   - 可能引起恐慌的虚假灾难图像
   - 选举相关的虚假信息图像

10. **网络安全威胁**：
    - 恶意软件或病毒相关图像
    - 钓鱼攻击相关内容
    - 网络攻击工具展示

请以JSON格式返回审核结果：
{
  "approved": true/false,
  "confidence": 0.0-1.0,
  "violations": ["具体违规类型"],
  "suggestions": "符合Google Veo政策的改进建议",
  "reason": "基于Google Veo政策的审核理由",
  "description": "图片内容描述",
  "veo_policy_compliance": "是否符合Google Veo内容政策"
}

审核严格程度: {strictness}
注意：必须遵守Google Veo的内容政策，确保用于视频生成的图片内容安全、合法且符合平台规范。**特别强调：只禁止使用广泛知名的名人、明星、重要公众人物的照片或肖像。普通个人或无法清楚识别为特定知名公众人物的人员应该被允许。**重点关注明确的政策违规，而非过度谨慎的解释。请用中文回复。`
    },

    // 英文审核提示词
    en: {
        textModeration: `You are a professional content moderator specializing in reviewing content for Google Veo video generation. Please strictly review the following text content according to Google Veo's content policies and terms of use:

Content: "{content}"

Please strictly review according to Google Veo video generation policies, focusing on the following prohibited content:

**Content types prohibited by Google Veo:**
1. **Violence and Dangerous Content**:
   - Extreme violence, graphic scenes, sensational violent content
   - Self-harm, suicide-related content, including eating disorder guidance
   - Dangerous activities or detailed instructions
   - Weapon manufacturing or usage guidance
   - Gratuitous violence against animals

2. **Adult and Sexual Content**:
   - Sexual suggestions or sexual behavior descriptions
   - Nudity or semi-nudity content
   - Adult entertainment related content
   - Pornography or erotic content
   - Sexual violence or sexual assault descriptions

3. **Hate Speech and Discrimination**:
   - Discrimination based on race, gender, religion, sexual orientation, etc.
   - Hate speech or incitement to hatred
   - Harassment, bullying, intimidation of individuals or groups
   - Dehumanizing statements or comparing protected groups to animals
   - Incitement to violent attacks

4. **Illegal Activities and Dangerous Guidance**:
   - Drug manufacturing, purchasing or sales guidance
   - Illegal weapons trading
   - Other criminal activities detailed instructions
   - Guidance for purchasing illegal items

5. **Misinformation and False Content**:
   - Medical misinformation conflicting with scientific consensus
   - Election-related false information and fraud allegations
   - False disaster information that could cause panic
   - Conspiracy theories or extreme views
   - Deepfake content and false news events
   - Impersonating others (including deceased persons) for deception

6. **Child Safety and Minor Protection**:
   - **Strictly prohibit any content involving minors**
   - Inappropriate content involving minors
   - Child exploitation related content
   - To protect minors, reject all content containing descriptions of children or teenagers

7. **Intellectual Property Infringement**:
   - Unauthorized use of copyrighted content
   - Trademark infringement content

8. **Intellectual Property Infringement**:
   - Unauthorized use of copyrighted content
   - Trademark infringement content

9. **Celebrity Portrait Rights and Identity Impersonation**:
    - **Prohibited to use photos or portraits of well-known celebrities and public figures**
    - Including but not limited to famous actors, singers, politicians, sports stars, major influencers, etc.
    - Focus on widely recognizable public figures, not ordinary individuals
    - Only reject if the person is clearly identifiable as a specific well-known celebrity or public figure
    - Prohibited to create deepfake content that could be mistaken for real persons
    - Requesting generation of specific real person's appearance or likeness
    - Content imitating or impersonating well-known individuals

10. **Privacy Violations and Surveillance**:
    - Sharing others' private information without consent
    - Malicious tracking or surveillance content
    - Content containing personal identity information
    - Unauthorized disclosure of private information

11. **Cybersecurity Threats**:
    - Spam, phishing, or malware related content
    - Abuse, harm, interference with, or disruption to services infrastructure
    - Attempts to circumvent abuse protections or safety filters

Please return the review result in JSON format:
{
  "approved": true/false,
  "confidence": 0.0-1.0,
  "violations": ["specific violation types"],
  "suggestions": "improvement suggestions compliant with Google Veo policies",
  "reason": "review reason based on Google Veo policies",
  "veo_policy_compliance": "whether it complies with Google Veo content policies"
}

Review strictness: {strictness}
Note: Must strictly comply with Google Veo's content policies to ensure generated video content is safe, legal, and compliant with platform standards. **Special emphasis: Strictly prohibit any content requests involving celebrities, stars, or public figures' portraits.** Please reply in English.`,

        imageModeration: `You are a professional image content moderator specializing in reviewing image content for Google Veo video generation. Please strictly review this image according to Google Veo's content policies and terms of use:

Please strictly review according to Google Veo video generation policies, focusing on the following prohibited image content:

**Image content prohibited by Google Veo:**
1. **Violence and Dangerous Scenes**:
   - Bloody, violent scenes, sensational violent content
   - Weapon displays or threatening gestures
   - Dangerous activities or extreme sports detailed display
   - Accident or disaster scenes
   - Gratuitous violence against animals scenes
   - Self-harm or suicide related images

2. **Adult and Sexual Content**:
   - Nudity or semi-nudity
   - Sexually suggestive poses or scenes
   - Adult entertainment related images
   - Pornography or erotic content
   - Sexual violence or sexual assault scenes

3. **Hate Symbols and Discriminatory Content**:
   - Hate symbols or signs
   - Discriminatory gestures or slogans
   - Extremist related images
   - Racist, sexist and other discriminatory content
   - Images inciting violence or hatred

4. **Illegal Activity Scenes**:
   - Drug-related items or manufacturing scenes
   - Illegal weapons
   - Criminal activity scenes
   - Illegal trading or smuggling scenes

5. **Child Safety and Minor Protection**:
   - **Strictly prohibit any image content containing minors**
   - Inappropriate scenes involving minors
   - Children in dangerous environments
   - To protect minors' privacy and safety, reject all images containing children or teenagers

6. **Privacy Violations and Surveillance**:
   - Unauthorized private photos
   - Images containing personal identification information
   - Malicious tracking or surveillance related images
   - Unauthorized biometric data

7. **Copyright Infringement**:
   - Copyrighted artworks
   - Trademark or brand logos
   - Movie, TV show screenshots
   - Unauthorized protected content

8. **Celebrity Portrait Rights and Identity Impersonation**:
   - **Prohibited to use photos or portraits of well-known celebrities and public figures**
   - Including but not limited to famous actors, singers, politicians, sports stars, major influencers, etc.
   - Focus on widely recognizable public figures, not ordinary individuals
   - Only reject if the person is clearly identifiable as a specific well-known celebrity or public figure
   - Deepfake images that could be mistaken for real persons

9. **Misleading Content and False Information**:
   - Deepfakes or AI-generated false images
   - Misleading medical images
   - Images of false news events
   - False disaster images that could cause panic
   - Election-related false information images

10. **Cybersecurity Threats**:
    - Malware or virus related images
    - Phishing attack related content
    - Network attack tool displays

Please return the review result in JSON format:
{
  "approved": true/false,
  "confidence": 0.0-1.0,
  "violations": ["specific violation types"],
  "suggestions": "improvement suggestions compliant with Google Veo policies",
  "reason": "review reason based on Google Veo policies",
  "description": "image content description",
  "veo_policy_compliance": "whether it complies with Google Veo content policies"
}

Review strictness: {strictness}
Note: Must comply with Google Veo's content policies to ensure image content used for video generation is safe, legal, and compliant with platform standards. **Special emphasis: Only prohibit the use of photos or portraits of widely recognizable celebrities, stars, or major public figures. Ordinary individuals or people who are not clearly identifiable as specific well-known public figures should be allowed.** Focus on clear policy violations rather than overly cautious interpretations. Please reply in English.`
    }
};

// 多语言响应消息
const MODERATION_MESSAGES = {
    zh: {
        contentModerationFailed: '内容审核失败',
        contentViolatesPolicy: '内容违反Google Veo内容政策',
        moderationServiceError: '内容审核服务不可用',
        unableToVerifyContent: '无法验证内容安全性',
        pleaseRetryLater: '请稍后重试',
        removeInappropriateContent: '请移除不当内容并使用更合适的表达方式',
        ensureVeoCompliance: '确保符合Google Veo视频生成政策',
        llmModerationNotConfigured: 'LLM智能审核未配置，仅进行了基础规则检查。建议配置MODERATION_LLM_API_KEY以启用完整的Google Veo政策审核。',
        textModerationDisabled: '文本审核已禁用',
        imageModerationDisabled: '图片审核已禁用'
    },
    en: {
        contentModerationFailed: 'Content moderation failed',
        contentViolatesPolicy: 'Content violates Google Veo content policy',
        moderationServiceError: 'Content moderation service unavailable',
        unableToVerifyContent: 'Unable to verify content safety',
        pleaseRetryLater: 'Please try again later',
        removeInappropriateContent: 'Please remove inappropriate content and use more suitable expressions',
        ensureVeoCompliance: 'Ensure compliance with Google Veo video generation policies',
        llmModerationNotConfigured: 'LLM intelligent moderation not configured, only basic rule checking performed. Recommend configuring MODERATION_LLM_API_KEY to enable full Google Veo policy review.',
        textModerationDisabled: 'Text moderation disabled',
        imageModerationDisabled: 'Image moderation disabled'
    }
};

// 语言检测函数
function detectLanguage(text) {
    if (!text) return 'en';

    // 简单的语言检测：检查是否包含中文字符
    const chineseRegex = /[\u4e00-\u9fff]/;
    if (chineseRegex.test(text)) {
        return 'zh';
    }

    return 'en';
}

// 审核严格程度配置
const STRICTNESS_LEVELS = {
    strict: {
        threshold: 0.3,
        description: '严格模式，对可疑内容零容忍'
    },
    normal: {
        threshold: 0.6,
        description: '正常模式，平衡安全性和可用性'
    },
    loose: {
        threshold: 0.8,
        description: '宽松模式，仅拦截明显违规内容'
    }
};

module.exports = {
    MODERATION_CONFIG,
    SENSITIVE_WORDS,
    MODERATION_PROMPTS,
    MODERATION_MESSAGES,
    STRICTNESS_LEVELS,
    detectLanguage
};
