# 视频生成异步中转服务

一个用于将上游流式视频生成接口转换为异步提交-查询模式的中转服务。

## 🐳 Docker 快速启动（推荐）

### 使用 Docker Compose 一键启动

```bash
# 1. 克隆项目
git clone <your-repo-url>
cd god-veo-async-transfer

# 2. 启动生产环境
./docker-start.sh

# 或启动开发环境（支持热重载）
./docker-start.sh dev
```

### Docker 命令说明

```bash
./docker-start.sh          # 启动生产环境 (默认端口 8847)
./docker-start.sh dev      # 启动开发环境 (默认端口 8848)
./docker-start.sh stop     # 停止所有服务
./docker-start.sh logs     # 查看生产环境日志
./docker-start.sh logs dev # 查看开发环境日志
./docker-start.sh rebuild  # 重新构建镜像
./docker-start.sh help     # 显示帮助信息
```

### 环境配置

首次运行时会自动创建 `.env` 文件，您可以根据需要修改配置：

```bash
# 服务端口配置
PORT=8847          # 生产环境端口
DEV_PORT=8848      # 开发环境端口

# Node.js 环境
NODE_ENV=production

# 日志级别
LOG_LEVEL=info
```

### Docker 特性

- ✅ **多阶段构建**: 优化镜像大小和构建速度
- ✅ **健康检查**: 自动监控服务状态
- ✅ **日志轮转**: 防止日志文件过大
- ✅ **非 root 用户**: 提高安全性
- ✅ **开发热重载**: 开发环境支持代码变更自动重启
- ✅ **优雅关闭**: 支持信号处理和优雅关闭

## 🚀 传统方式启动

如果您不使用 Docker，也可以通过传统方式启动：

### 1. 安装依赖

```bash
npm install
```

### 2. 启动服务

```bash
npm start
```

服务将运行在 `http://localhost:8847`

### 3. 测试服务

```bash
# 设置API Key并运行测试
API_KEY=sk-your-api-key-here npm test

# 或者指定测试模式
API_KEY=sk-your-api-key-here node test-client.js proxy    # 通过中转服务查询
API_KEY=sk-your-api-key-here node test-client.js direct   # 直接轮询上游
API_KEY=sk-your-api-key-here node test-client.js list     # 查看任务列表
API_KEY=sk-your-api-key-here node test-client.js video    # 测试标准视频创建API
API_KEY=sk-your-api-key-here node test-client.js all      # 运行所有测试
```

## 📋 API 接口

服务提供两套API接口：

### Chat风格接口（兼容原有逻辑）

#### 提交任务

```bash
curl -X POST http://localhost:8847/api/video/submit \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-your-api-key-here" \
  -d '{
    "prompt": "A beautiful sunset over the mountains",
    "model": "veo2"
  }'
```

#### 查询状态

```bash
curl http://localhost:8847/api/video/status/{taskId}
```

### 标准视频API接口（推荐用于对外暴露）

**重要**: 必须在请求头中包含 `Authorization: Bearer {your-api-key}`，服务会将此头信息直接转发给上游服务。

**缺少Authorization时的错误响应:**
```json
{
  "success": false,
  "error": "Authorization header is required"
}
```

**响应示例:**
```json
{
  "success": true,
  "data": {
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "upstreamTaskId": "veo2:6864c9b2-9528-4f9c-bdf0-0fa295e14adf",
    "pollingUrl": "https://asyncdata.net/source/veo2:6864c9b2-9528-4f9c-bdf0-0fa295e14adf",
    "status": "processing",
    "message": "Task submitted successfully, upstream task created"
  }
}
```

### 查询状态

```bash
curl http://localhost:8847/api/video/status/{taskId}
```

**处理中响应:**
```json
{
  "success": true,
  "data": {
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "processing",
    "prompt": "A beautiful sunset over the mountains",
    "upstreamTaskId": "veo2:6864c9b2-9528-4f9c-bdf0-0fa295e14adf",
    "pollingUrl": "https://asyncdata.net/source/veo2:6864c9b2-9528-4f9c-bdf0-0fa295e14adf",
    "progress": {
      "upstreamStatus": "video_generating",
      "videoGenerationStatus": "MEDIA_GENERATION_STATUS_ACTIVE",
      "retryCount": 0,
      "maxRetries": 3
    },
    "upstreamData": { ... }
  }
}
```

**完成响应:**
```json
{
  "success": true,
  "data": {
    "taskId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "result": {
      "video_url": "https://filesystem.site/cdn/20250629/example.mp4",
      "video_media_id": "..."
    }
  }
}
```

### 其他接口

- `GET /api/video/tasks` - 获取任务列表
- `DELETE /api/video/task/{taskId}` - 删除任务
- `GET /health` - 健康检查

## 🔧 工作原理

1. **提交任务** → 向上游发起流请求
2. **解析信息** → 从流数据中提取Task ID和轮询URL
3. **立即断开** → 获取必要信息后立即取消上游连接
4. **返回信息** → 将上游Task ID和轮询URL返回给客户端
5. **灵活轮询** → 客户端可选择自行轮询或通过中转服务查询

## 💡 使用场景

### Chat风格接口（兼容原有逻辑）

#### 方式1: 通过中转服务查询（推荐）

```javascript
const client = new VideoApiClient('http://localhost:8847', 'sk-your-api-key-here');

// 提交任务
const result = await client.submitTask('A cat playing');
const taskId = result.data.taskId;

// 轮询状态
const completed = await client.waitForCompletion(taskId);
console.log('Video URL:', completed.result.video_url);
```

#### 方式2: 直接轮询上游

```javascript
const client = new VideoApiClient('http://localhost:8847', 'sk-your-api-key-here');

// 提交任务  
const result = await client.submitTask('A cat playing');
const pollingUrl = result.data.pollingUrl;

// 直接轮询上游
const completed = await client.waitForCompletionDirect(pollingUrl);
console.log('Video URL:', completed.result.video_url);
```

### 标准视频API（推荐用于对外服务）

```javascript
const client = new VideoApiClient('http://localhost:8847', 'sk-your-api-key-here');

// 创建视频
const videoParams = {
  prompt: "A majestic eagle soaring through mountain peaks",
  model: "veo2-fast",
  images: [
    "https://filesystem.site/cdn/20250612/VfgB5ubjInVt8sG6rzMppxnu7gEfde.png"
  ],
  enhance_prompt: true
};

const createResult = await client.createVideo(videoParams);
const videoId = createResult.id;

// 等待完成
const completedVideo = await client.waitForVideoCompletion(videoId);
console.log('Video URL:', completedVideo.video_url);
```

## ⚙️ 配置

服务无需配置即可使用，API Key 通过客户端请求头传递：

```bash
# 环境变量方式（用于测试）
export API_KEY=sk-your-api-key-here

# 或在请求头中直接传递
Authorization: Bearer sk-your-api-key-here
```

## 📝 注意事项

1. **API Key 安全**:
    - API Key 通过请求头传递，服务不存储任何认证信息
    - 确保在生产环境中妥善保管 API Key
    - 可为不同用户使用不同的 API Key

2. **数据持久化**: 服务使用内存存储，重启会丢失数据。

3. **轮询策略**:
    - 状态查询接口会实时从上游获取状态，有一定网络开销
    - 客户端可选择直接使用返回的轮询URL进行轮询，减少中转开销
    - 建议轮询间隔不少于1秒，避免对上游服务造成压力

4. **超时处理**:
    - 提交接口等待上游响应的超时时间为30秒
    - 客户端轮询建议设置合理的超时时间（如5分钟）

5. **连接管理**: 服务会在获取到必要信息后立即断开上游连接，避免资源浪费。

6. **并发处理**: 可根据需要添加并发限制和速率限制。

## 🛠️ 开发

```bash
# 开发模式（自动重启）
npm run dev

# 或者使用 nodemon
npx nodemon server.js
```

## 🐳 Docker 部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 8847
CMD ["npm", "start"]
```

```bash
docker build -t video-proxy .
docker run -p 8847:8847 video-proxy
```
