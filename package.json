{"name": "video-proxy-server", "version": "1.0.0", "description": "异步视频生成中转服务", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo 'Please set API_KEY environment variable: API_KEY=sk-your-key node test-client.js' && node test-client.js", "test-env": "node scripts/test-env.js", "check-moderation": "node scripts/check-moderation-config.js", "test-moderation": "node examples/moderation-examples.js", "test-multilingual": "node scripts/test-multilingual.js", "test-json-parsing": "node scripts/test-json-parsing.js"}, "dependencies": {"dotenv": "^16.3.1", "express": "^4.18.2", "node-fetch": "^2.6.7", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["video", "proxy", "async", "api"], "author": "", "license": "MIT"}