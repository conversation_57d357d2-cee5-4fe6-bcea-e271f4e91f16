# 🚨 内容审核配置快速修复指南

## 问题：调用 `/api/moderation/test` 提示 "LLM moderation not configured"

### 🔍 问题诊断

运行配置检查工具：
```bash
npm run check-moderation
```

这会显示详细的配置状态和问题。

### ⚡ 快速解决方案

#### 方案1：配置OpenAI API密钥（推荐）

1. **安装依赖**
   ```bash
   npm install
   ```

2. **获取OpenAI API密钥**
   - 访问：https://platform.openai.com/api-keys
   - 创建新的API密钥

3. **创建.env文件**（推荐方法）
   ```bash
   # 创建.env文件
   cat > .env << EOF
   MODERATION_LLM_API_KEY=sk-your-openai-api-key-here
   MODERATION_ENABLED=true
   MODERATION_STRICTNESS=normal
   EOF
   ```

4. **或者设置环境变量**
   ```bash
   export MODERATION_LLM_API_KEY="sk-your-openai-api-key-here"
   ```

5. **重启服务**
   ```bash
   npm start
   ```

#### 方案2：使用其他兼容API服务

如果你有其他兼容OpenAI格式的API服务：
```bash
export MODERATION_LLM_API_KEY="your-api-key"
export MODERATION_LLM_URL="https://your-api-endpoint.com/v1/chat/completions"
export MODERATION_LLM_MODEL="your-model-name"
```

#### 方案3：临时使用基础审核模式

如果暂时没有LLM API，系统现在会自动降级到基础审核模式：
- ✅ 敏感词检测仍然工作
- ✅ 基本规则验证仍然工作  
- ⚠️ 缺少AI智能分析
- ⚠️ Google Veo政策合规性检查受限

### 🧪 测试修复结果

1. **测试配置**
   ```bash
   npm run check-moderation
   ```

2. **测试审核功能**
   ```bash
   # 测试文字审核
   curl -X POST http://localhost:8847/api/moderation/test \
     -H "Content-Type: application/json" \
     -d '{"text": "一个美丽的风景视频"}'
   
   # 测试图片审核
   curl -X POST http://localhost:8847/api/moderation/test \
     -H "Content-Type: application/json" \
     -d '{"imageUrl": "https://example.com/image.jpg"}'
   ```

3. **运行完整示例**
   ```bash
   npm run test-moderation
   ```

4. **测试多语言功能**
   ```bash
   npm run test-multilingual
   ```

### 📋 完整的.env配置示例

创建或编辑 `.env` 文件：
```bash
# 基础配置
PORT=8847
MODERATION_ENABLED=true
MODERATION_STRICTNESS=normal

# LLM配置（必需）
MODERATION_LLM_API_KEY=sk-your-openai-api-key-here
MODERATION_LLM_MODEL=gpt-4o-mini
MODERATION_LLM_URL=https://api.openai.com/v1/chat/completions
MODERATION_LLM_TIMEOUT=10000

# 图片审核配置
IMAGE_MODERATION_ENABLED=true
IMAGE_DOWNLOAD_TIMEOUT=15000
IMAGE_MAX_SIZE=10485760

# 缓存配置
MODERATION_CACHE_ENABLED=true
MODERATION_CACHE_TTL=3600
```

### 🔧 常见问题排查

#### 问题1：API密钥无效
```bash
# 检查API密钥格式
echo $MODERATION_LLM_API_KEY
# 应该以 sk- 开头

# 测试API密钥
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer $MODERATION_LLM_API_KEY"
```

#### 问题2：网络连接问题
```bash
# 测试网络连接
curl -I https://api.openai.com/v1/chat/completions

# 如果使用代理
export https_proxy=http://your-proxy:port
```

#### 问题3：模型不存在
```bash
# 检查可用模型
curl https://api.openai.com/v1/models \
  -H "Authorization: Bearer $MODERATION_LLM_API_KEY" | grep gpt-4
```

### 🚀 验证修复成功

修复后，你应该看到：

1. **配置检查通过**
   ```bash
   npm run check-moderation
   # 输出：✅ 配置检查通过，系统可以正常运行
   ```

2. **审核测试成功**
   ```bash
   curl -X POST http://localhost:8847/api/moderation/test \
     -H "Content-Type: application/json" \
     -d '{"text": "测试内容"}'
   
   # 期望输出：
   # {
   #   "success": true,
   #   "data": {
   #     "approved": true,
   #     "confidence": 0.9,
   #     "veo_policy_compliance": "compliant"
   #   }
   # }
   ```

3. **服务启动日志正常**
   ```
   === Moderation Status ===
   Enabled: true
   Strictness: normal
   Image moderation: true
   Cache enabled: true
   ```

### 📞 需要帮助？

如果问题仍然存在：

1. **查看详细日志**
   ```bash
   DEBUG=* npm start
   ```

2. **检查服务状态**
   ```bash
   curl http://localhost:8847/api/moderation/stats
   ```

3. **重置配置**
   ```bash
   # 清理缓存
   curl -X POST http://localhost:8847/api/moderation/clear-cache
   
   # 重启服务
   npm start
   ```

### ✅ 修复完成检查清单

- [ ] API密钥已配置且有效
- [ ] 网络连接正常
- [ ] 配置检查通过
- [ ] 审核测试成功
- [ ] 服务启动正常
- [ ] 日志无错误信息

完成以上步骤后，你的内容审核系统应该可以正常工作了！
