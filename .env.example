# 服务端口配置
PORT=8847
DEV_PORT=8848

# Node.js 环境
NODE_ENV=production

# 日志级别
LOG_LEVEL=info

# 内容审核配置
# 是否启用内容审核 (true/false)
MODERATION_ENABLED=true

# 审核严格程度 (strict/normal/loose)
MODERATION_STRICTNESS=normal

# 大模型审核配置
# 审核模型API地址
MODERATION_LLM_URL=https://api.openai.com/v1/chat/completions

# 审核模型名称
MODERATION_LLM_MODEL=gpt-4o-mini

# 审核模型API密钥
MODERATION_LLM_API_KEY=your-api-key-here

# 审核请求超时时间(毫秒)
MODERATION_LLM_TIMEOUT=10000

# 审核最大重试次数
MODERATION_LLM_MAX_RETRIES=2

# 分类审核开关配置
# 是否启用文本审核 (true/false)
TEXT_MODERATION_ENABLED=true

# 图片审核配置
# 是否启用图片审核 (true/false)
IMAGE_MODERATION_ENABLED=true

# 图片下载超时时间(毫秒)
IMAGE_DOWNLOAD_TIMEOUT=15000

# 图片最大大小(字节)
IMAGE_MAX_SIZE=10485760

# 审核缓存配置
# 是否启用审核缓存 (true/false)
MODERATION_CACHE_ENABLED=true

# 缓存过期时间(秒)
MODERATION_CACHE_TTL=3600
