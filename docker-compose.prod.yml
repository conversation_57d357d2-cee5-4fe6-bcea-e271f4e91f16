version: '3.8'

services:
  video-proxy:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: video-proxy-server-prod
    ports:
      - "${PORT:-8847}:8847"
    environment:
      - NODE_ENV=production
      - PORT=8847
    env_file:
      - .env
    restart: unless-stopped
    networks:
      - video-proxy-network
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:8847/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # 生产环境安全设置
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp:noexec,nosuid,size=100m
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # 可选：添加 Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: video-proxy-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - video-proxy
    networks:
      - video-proxy-network
    restart: unless-stopped
    profiles:
      - nginx

networks:
  video-proxy-network:
    driver: bridge 